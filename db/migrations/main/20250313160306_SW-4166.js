/**
 * @param { import("knex").Knex } knex
 * @returns {Knex.SchemaBuilder}
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TABLE IF NOT EXISTS "public"."club_history"
        (
            "club_history_id"   INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
            "created"           TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            "modified"          TIMESTAMP WITHOUT TIME ZONE,
            "master_club_id"    INT NOT NULL,
            "master_athlete_id" INT,
            "master_staff_id"   INT,
            "master_team_id"    INT,
            "action"            TEXT,
            "notes"             TEXT
        );
        
        CREATE TRIGGER update_club_history_modified
            BEFORE UPDATE
            ON "public"."club_history"
            FOR EACH ROW
        EXECUTE PROCEDURE update_modified_column();
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns {Knex.SchemaBuilder}
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."club_history";
    `);
};
