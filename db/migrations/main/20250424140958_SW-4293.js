/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        DROP TRIGGER IF EXISTS remove_staff_roles ON master_staff;
        DROP FUNCTION IF EXISTS remove_staff_roles();
    `);
  
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(
        `CREATE OR REPLACE FUNCTION remove_staff_roles () RETURNS TRIGGER LANGUAGE plpgsql AS $$
             DECLARE 
              msr_removed INTEGER;
              rsr_removed INTEGER;
             BEGIN
                IF ( (TG_OP = 'UPDATE' AND NEW."deleted" IS NOT NULL) OR (TG_OP = 'DELETE') ) THEN 
                 -- remove existing master_staff_roles first;
                 WITH "m_rows" AS (
                  DELETE FROM "master_staff_role" msr 
                  WHERE msr."master_staff_id" = OLD."master_staff_id" 
                  RETURNING 1
                 )
                 SELECT COUNT(*) INTO msr_removed FROM "m_rows";
                 RAISE INFO 'Removed % "master_staff_role" rows', msr_removed; 
        
                 -- remove existing event roles;
                 WITH "r_rows" AS (
                  UPDATE "roster_staff_role" 
                  SET "deleted" = NOW()
                  WHERE "roster_staff_role_id" IN (
                   SELECT rsr."roster_staff_role_id" 
                   FROM "roster_staff_role" rsr 
                   INNER JOIN "roster_team" rt 
                    ON rt.roster_team_id = rsr.roster_team_id 
                   INNER JOIN "division" d 
                    ON d.division_id = rt.division_id 
                   INNER JOIN "event" e 
                    ON e.event_id = rt.event_id 
                    AND e.event_id = d.event_id
                   WHERE rsr.master_staff_id = OLD."master_staff_id"
                    AND COALESCE(d.roster_deadline, e.roster_deadline) >= (NOW() AT TIME ZONE e.timezone)
                  )
                  AND "master_staff_id" = OLD."master_staff_id"
                  RETURNING 1
                 )
                 SELECT COUNT(*) INTO rsr_removed FROM "r_rows";
                 RAISE INFO 'Removed % "roster_staff_role" rows', rsr_removed; 
                END IF;
                RETURN NULL;
            END;
     $$;
     
     CREATE TRIGGER "remove_staff_roles"
     AFTER UPDATE OF "deleted" OR DELETE ON "master_staff"  
     FOR EACH ROW EXECUTE PROCEDURE remove_staff_roles();
   `)
};
