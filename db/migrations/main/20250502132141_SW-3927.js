
exports.up = function(knex) {
    return knex.raw(`
        UPDATE email_template_group
        SET variables = variables ||
            jsonb_build_object(
                'field', 'additional_fields',
                'title', 'Additional fields',
                'pattern', '{additional_fields}',
                'custom_action', true,
                'is_hidden', true
            )
        WHERE "group" = 'tickets.payments';
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        UPDATE email_template_group
        SET variables = '[
            {
                "field": "event_name",
                "title": "Event Name",
                "pattern": "{event_name}",
                "is_available_for_subject": true
            },
            {
                "field": "tickets_receipt_descr",
                "title": "Tickets Receipt Description",
                "pattern": "{tickets_receipt_descr}",
                "custom_action": true
            },
            {
                "field": "payer",
                "title": "Payer''s Name",
                "pattern": "{payer}",
                "is_available_for_subject": true
            },
            {
                "field": "tickets_names_list",
                "title": "Bought Ticket Types List",
                "pattern": "{tickets_names_list}",
                "custom_action": true
            },
            {
                "field": "tickets_links",
                "title": "Tickets Links",
                "pattern": "{tickets_links}",
                "custom_action": true
            },
            {
                "field": "total_amount",
                "title": "Total Purchase Amount",
                "pattern": "{total_amount}"
            },
            {
                "field": "service_fee",
                "title": "Purchase Service Fee",
                "pattern": "{service_fee}"
            },
            {
                "field": "credit_card_merchant_fee",
                "title": "Credit Card Merchant Fee",
                "pattern": "{credit_card_merchant_fee}"
            },
            {
                "field": "social_icons",
                "title": "Social Icons",
                "pattern": "{social_icons}",
                "custom_action": true
            },
            {
                "field": "facebook_icon",
                "title": "Facebook Icon",
                "pattern": "{facebook_icon}",
                "custom_action": true
            },
            {
                "field": "twitter_icon",
                "title": "Twitter Icon",
                "pattern": "{twitter_icon}",
                "custom_action": true
            },
            {
                "field": "instagram_icon",
                "title": "Instagram Icon",
                "pattern": "{instagram_icon}",
                "custom_action": true
            },
            {
                "field": "snapchat_icon",
                "title": "Snapchat Icon",
                "pattern": "{snapchat_icon}",
                "custom_action": true
            },
            {
                "field": "event_logo",
                "title": "Event''s Logo Image",
                "pattern": "{event_logo}",
                "custom_action": true
            },
            {
                "field": "event_dates_info",
                "title": "Event''s dates, city and state",
                "pattern": "{event_dates_info}",
                "custom_action": true
            },
            {
                "field": "ticket_holder_name",
                "title": "Tickets holder name",
                "pattern": "{ticket_holder_name}",
                "is_available_for_subject": true
            },
            {
                "field": "qr_code_image",
                "title": "Ticket QR code image",
                "pattern": "{qr_code_image}",
                "custom_action": true
            },
            {
                "field": "valid_dates",
                "title": "Ticket type valid dates (when ticket is valid for scan)",
                "pattern": "{valid_dates}"
            },
            {
                "field": "ticket_barcode",
                "title": "Ticket barcode",
                "pattern": "{ticket_barcode}"
            },
            {
                "field": "payer_phone",
                "title": "Ticket purchaser phone number",
                "pattern": "{payer_phone}"
            },
            {
                "field": "payer_zip",
                "title": "Ticket purchaser zip code",
                "pattern": "{payer_zip}"
            },
            {
                "field": "payer_email",
                "title": "Ticket purchaser email",
                "pattern": "{payer_email}"
            },
            {
                "field": "payment_date_time",
                "title": "Tickets payment date/time",
                "pattern": "{payment_date_time}"
            },
            {
                "field": "ticket_price",
                "title": "Ticket price",
                "pattern": "{ticket_price}"
            },
            {
                "field": "statement_decriptor",
                "title": "Statement descriptor",
                "pattern": "{statement_decriptor}"
            },
            {
                "field": "apple_wallet_icon",
                "title": "Add To Apple Wallet Icon",
                "pattern": "{apple_wallet_icon}",
                "custom_action": true
            },
            {
                "field": "account_activation_link",
                "title": "Account Activation Link",
                "pattern": "{account_activation_link}",
                "is_hidden": true,
                "custom_action": true
            }
        ]'::JSONB
        WHERE "group" = 'tickets.payments';
    `);
};
