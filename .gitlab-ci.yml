stages:
  - audit
  - test
  - deploy
  - reload
  - sales-hub-integration-test


semgrep:
  stage: audit
  when: manual
  script:
    - docker run --rm -v "${PWD}:/src" docker.io/returntocorp/semgrep semgrep -c p/security-audit

deploy:node:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - api/**/*
      - api/**/**/*
      - config/**/*
      - userconfig/routes/**/*
      - scheduler/**/*
      - scheduler/**/**/*
      - sw-utils/**/*
      - views/**/*
      - app.js
      - officials-schedule-export.js
      - ecosystem-prod.json
      - deploy/**/*
      - .gitlab-ci.yml
      - Dockerfile
  script:
    - ansible-playbook -l marc-aws-sw deploy/node.yml

deploy:node_npm:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - package.json
      - package-lock.json
  script:
    - ansible-playbook -l marc-aws-sw deploy/node_npm_i.yml
  dependencies:
    - deploy:node

reload:prod:
    stage: reload
    only:
        refs:
        - master
        changes:
        - package.json
        - package-lock.json
        - api/**/*
        - api/**/**/*
        - config/**/*
        - userconfig/routes/**/*
        - scheduler/**/*
        - scheduler/**/**/*
        - sw-utils/**/*
        - views/**/*
        - app.js
        - officials-schedule-export.js
        - ecosystem-prod.json
        - deploy/**/*
        - .gitlab-ci.yml
        - Dockerfile
    script:
      - ansible-playbook -l marc-aws-sw deploy/pm2_reload.yml -e "REDIS_URL=$REDIS_URL_PROD" -e "HOST_PORT=$HOST_PORT_PROD" -e "EMAIL_REDIS_URL=$EMAIL_REDIS_URL_PROD" -e "SW_DB=$SW_DB_PROD" -e "NODE_ENV=production" -e "LOG_PG_CS=$LOG_PG_CS_PROD" -e "LOG_APP_ID=$LOG_APP_ID"
    dependencies:
      - deploy:node
      - deploy:node_npm

deploy:static:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - bower.json
      - .bowerrc
      - webpack.config.base.js
      - webpack.config.frontend.js
      - assets/**/*
      - assets/**/**/*
      - frontend/**/*
      - frontend/**/**/*
  script:
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env EMAIL_DB=$EMAIL_DB_PROD \
        --env SW_DB=$SW_DB_PROD \
        --env ENV=production \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:frontend'
    - ansible-playbook -l marc-aws-sw deploy/static.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_events:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - bower.json
      - .bowerrc
      - webpack.config.base.js
      - webpack.config.frontend_event.js
      - assets/**/*
      - assets/**/**/*
      - frontend_event/**/*
      - frontend_event/**/**/*
  script:
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env EMAIL_DB=$EMAIL_DB_PROD \
        --env SW_DB=$SW_DB_PROD \
        --env ENV=production \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:event'
    - ansible-playbook -l marc-aws-sw deploy/static_esw.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_admin:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - bower.json
      - .bowerrc
      - webpack.config.base.js
      - webpack.config.frontend_admin.js
      - assets/**/*
      - assets/**/**/*
      - frontend_admin/**/*
      - frontend_admin/**/**/*
  script:
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env EMAIL_DB=$EMAIL_DB_PROD \
        --env SW_DB=$SW_DB_PROD \
        --env ENV=production \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:admin'
    - ansible-playbook -l marc-aws-sw deploy/static_asw.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:node_stage_npm:
  stage: deploy
  only:
    refs:
      - staging
    changes:
      - package.json
      - package-lock.json
  script:
    - ansible-playbook deploy/node_stage_npm_i.yml

deploy:node_stage:
  stage: deploy
  only:
    refs:
      - staging
    changes:
      - api/**/*
      - api/**/**/*
      - config/**/*
      - userconfig/routes/**/*
      - scheduler/**/*
      - scheduler/**/**/*
      - sw-utils/**/*
      - views/**/*
      - app.js
      - officials-schedule-export.js
      - ecosystem-dev.json
  script:
    - ansible-playbook -l marc-aws-sw-stage deploy/node_stage.yml
  dependencies:
    - deploy:node_stage_npm

deploy:static_stage:
  stage: deploy
  only:
    refs:
      - staging
    changes:
      - bower.json
      - .bowerrc
      - webpack.config.base.js
      - webpack.config.frontend.js
      - assets/**/*
      - assets/**/**/*
      - frontend/**/*
      - frontend/**/**/*
  script:
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env ENV=production \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:frontend'
    - ansible-playbook -l marc-aws-sw-stage deploy/static_stage.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_events_stage:
  stage: deploy
  only:
    refs:
      - staging
    changes:
      - bower.json
      - .bowerrc
      - webpack.config.base.js
      - webpack.config.frontend_event.js
      - assets/**/*
      - assets/**/**/*
      - frontend_event/**/*
      - frontend_event/**/**/*
  script:
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env ENV=production \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:event'
    - ansible-playbook -l marc-aws-sw-stage deploy/static_esw_stage.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

#deploy:static_admin_stage:
#  stage: deploy
#  only:
#    refs:
#      - staging
#    changes:
#      - bower.json
#      - .bowerrc
#      - tasks/**/*
#      - Gruntfile.js
#      - assets/**/*
#      - assets/**/**/*
#      - frontend_admin/**/*
#      - frontend_admin/**/**/*
#  script:
#    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root && npm rebuild node-sass;'"
#    - "docker run --rm --env 'EMAIL_DB=postgres://sw:<EMAIL>:5432/emailqueue' --env 'SW_DB=postgres://sw:<EMAIL>:5432/sw' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt prod_asw;'"
#    - ansible-playbook -l marc-aws-sw-stage deploy/static_asw_stage.yml
#  cache:
#  key:
#    files:
#     - package-lock.json
#     - bower.json
#  paths:
#    - node_modules/
#    - bower_components/

test:migration:
  stage: test
  only:
    refs:
      - development
    changes:
      - db/migrations/main/*
  script:
    - "docker run --rm -v `pwd`:/app -w /app --env SW_DB=$TEST_DB_CONNECTION -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 sh -c 'mkdir /app/logs && npm install knex --legacy-peer-deps && npm run migrate-main'"

test:node:
 only:
   refs:
     - development
   variables:
     - $SKIP_TESTS != "true"
 cache:
   key: "$CI_COMMIT_REF_SLUG"
   paths:
      - node_modules/
 resource_group: test
 stage: test
 script:
    - "docker run --rm -v `pwd`:/app -w /app --env REDIS_URL=$REDIS_URL_DEV --env RABBITMQ_URL=$RABBITMQ_URL_DEV --env EMAIL_REDIS_URL=$EMAIL_REDIS_URL_DEV --env TEST_NODE_ENV=development --env TEST_DB_CONNECTION --env SW_LOGGER_FILE_PATH=./ --env SW_DB=false -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 sh -c 'mkdir /app/logs && mkdir -p /app/uploads/rosterImport && npm ci --legacy-peer-deps && npm run test'"

trigger_integration_tests:dev:
  stage: sales-hub-integration-test
  only:
    - development
  trigger:
    project: sw-web/sales-hub-integration-tests
    branch: development
    strategy: depend

deploy:migration:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - db/migrations/main/*
  script:
    - "docker run --rm -v `pwd`:/app -w /app --env SW_DB=$SW_DB_DEV -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 sh -c 'mkdir /app/logs && npm install knex --legacy-peer-deps && npm run migrate-main'"

deploy:node_dev:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - api/**/*
      - api/**/**/*
      - config/**/*
      - userconfig/routes/**/*
      - scheduler/**/*
      - scheduler/**/**/*
      - sw-utils/**/*
      - views/**/*
      - app.js
      - officials-schedule-export.js
      - ecosystem-dev.json
      - deploy/**/*
      - .gitlab-ci.yml
      - Dockerfile
  script:
    - ansible-playbook -l marc-do-dev-sw-sw deploy/node.yml

deploy:node_dev_npm:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - package.json
      - package-lock.json
  script:
    - ansible-playbook -l marc-do-dev-sw-sw deploy/node_npm_i.yml
  dependencies:
    - deploy:node_dev

reload:dev:
    stage: reload
    only:
        refs:
        - development
        changes:
        - package.json
        - package-lock.json
        - api/**/*
        - api/**/**/*
        - config/**/*
        - userconfig/routes/**/*
        - scheduler/**/*
        - scheduler/**/**/*
        - sw-utils/**/*
        - views/**/*
        - app.js
        - officials-schedule-export.js
        - ecosystem-dev.json
        - deploy/**/*
        - .gitlab-ci.yml
        - Dockerfile
    script:
      - ansible-playbook -l marc-do-dev-sw-sw deploy/pm2_reload_dev.yml -e "REDIS_URL=$REDIS_URL_DEV" -e "HOST_PORT=$HOST_PORT_DEV" -e "EMAIL_REDIS_URL=$EMAIL_REDIS_URL_DEV" -e "SW_DB=$SW_DB_DEV" -e "NODE_ENV=development" -e "LOG_PG_CS=$LOG_PG_CS_DEV" -e "LOG_APP_ID=$LOG_APP_ID"
    dependencies:
      - deploy:node_dev
      - deploy:node_dev_npm

deploy:static_dev:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - bower.json
      - .bowerrc
      - webpack.config.base.js
      - webpack.config.frontend.js
      - assets/**/*
      - assets/**/**/*
      - frontend/**/*
      - frontend/**/**/*

  script:
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env EMAIL_DB=$EMAIL_DB_DEV \
        --env SW_DB=$SW_DB_DEV \
        --env REDIS_URL=$REDIS_URL_DEV \
        --env URLS=$URLS_DEV \
        --env ENV=development \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:frontend'
    - ansible-playbook -l marc-aws-sw-dev deploy/static.yml
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env EMAIL_DB=$EMAIL_DB_DEV \
        --env SW_DB=$SW_DB_DEV \
        --env REDIS_URL=$REDIS_URL_DEV \
        --env URLS=$URLS_DEV_DO \
        --env ENV=development \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:frontend'
    - ansible-playbook -l marc-do-dev-sw-sw deploy/static.yml
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_events_dev:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - bower.json
      - .bowerrc
      - webpack.config.base.js
      - webpack.config.frontend_event.js
      - assets/**/*
      - assets/**/**/*
      - frontend_event/**/*
      - frontend_event/**/**/*

  script:
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env SW_DB=$SW_DB_DEV \
        --env REDIS_URL=$REDIS_URL_DEV \
        --env URLS=$URLS_DEV \
        --env ENV=development \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:event'
    - ansible-playbook -l marc-aws-sw-dev deploy/static_esw.yml
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env SW_DB=$SW_DB_DEV \
        --env REDIS_URL=$REDIS_URL_DEV \
        --env URLS=$URLS_DEV_DO \
        --env ENV=development \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:event'
    - ansible-playbook -l marc-do-dev-sw-sw deploy/static_esw.yml
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_admin_dev:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - bower.json
      - .bowerrc
      - webpack.config.base.js
      - webpack.config.frontend_admin.js
      - assets/**/*
      - assets/**/**/*
      - frontend_admin/**/*
      - frontend_admin/**/**/*

  script:
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env SW_DB=$SW_DB_DEV \
        --env REDIS_URL=$REDIS_URL_DEV \
        --env URLS=$URLS_DEV \
        --env ENV=development \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:admin'
    - ansible-playbook -l marc-aws-sw-dev deploy/static_asw_dev.yml
    - |
      docker run --rm \
        -v `pwd`:/build \
        -w /build \
        -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env NPM_CONFIG_CACHE=/build/.npm \
        --env BOWER_CACHE=/build/.bower \
        --env SW_DB=$SW_DB_DEV \
        --env REDIS_URL=$REDIS_URL_DEV \
        --env URLS=$URLS_DEV_DO \
        --env ENV=development \
        node:22 \
        bash -c 'mkdir -p /build/.npm /build/.bower && npm ci && bower install --allow-root && npm run build:admin'
    - ansible-playbook -l marc-do-dev-sw-sw deploy/static_asw_dev.yml
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:doc_dev:
  stage: deploy
  only:
      refs:
          - development
      changes:
          - userconfig/**/*
          - apidoc.json
  script:
      - "docker run --rm -v `pwd`:/build -w /build -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 /bin/bash -c 'npm i --legacy-peer-deps && npm run doc;'"
      - ansible-playbook -l marc-do-dev-sw-sw deploy/doc_dev.yml
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/


test:migration_dev2:
    stage: test
    only:
        refs:
            - development2
        changes:
            - db/migrations/main/*
    script:
        - "docker run --rm -v `pwd`:/app -w /app --env SW_DB=$TEST2_DB_CONNECTION -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 sh -c 'mkdir /app/logs && npm install knex --legacy-peer-deps && npm run migrate-main'"

# test:node_dev2:
#     only:
#         refs:
#             - development2
#     cache:
#         key: "$CI_COMMIT_REF_SLUG"
#         paths:
#             - node_modules/
#     stage: test
#     script:
#         - "docker run --rm -v `pwd`:/app -w /app --env REDIS_URL=$REDIS_URL_DEV --env EMAIL_REDIS_URL=$EMAIL_REDIS_URL_DEV2 --env TEST_NODE_ENV=development --env TEST_DB_CONNECTION=$TEST2_DB_CONNECTION --env SW_LOGGER_FILE_PATH=./ --env SW_DB=false -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 sh -c 'mkdir /app/logs && npm ci --legacy-peer-deps && npm run test'"

deploy:migration_dev2:
    stage: deploy
    only:
        refs:
            - development2
        changes:
            - db/migrations/main/*
    script:
        - "docker run --rm -v `pwd`:/app -w /app --env SW_DB=$SW_DB_DEV2 -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 sh -c 'mkdir /app/logs && npm install knex --legacy-peer-deps && npm run migrate-main'"

deploy:node_dev2:
    stage: deploy
    only:
        refs:
            - development2
        changes:
            - api/**/*
            - api/**/**/*
            - config/**/*
            - userconfig/routes/**/*
            - scheduler/**/*
            - scheduler/**/**/*
            - sw-utils/**/*
            - views/**/*
            - app.js
            - officials-schedule-export.js
            - ecosystem-dev.json
            - deploy/**/*
            - .gitlab-ci.yml
            - Dockerfile
            - docker-compose-2.yml
    script:
        - ansible-playbook -l marc-do-dev-sw-sw deploy/node.yml --extra-vars "SUFFIX=-2"

deploy:node_dev2_npm:
    stage: deploy
    only:
        refs:
            - development2
        changes:
            - package.json
            - package-lock.json
    script:
        - ansible-playbook -l marc-do-dev-sw-sw deploy/node_npm_i.yml --extra-vars "SUFFIX=-2"
    dependencies:
        - deploy:node_dev2

reload:dev2:
    stage: reload
    only:
        refs:
            - development2
        changes:
            - package.json
            - package-lock.json
            - api/**/*
            - api/**/**/*
            - config/**/*
            - userconfig/routes/**/*
            - scheduler/**/*
            - scheduler/**/**/*
            - sw-utils/**/*
            - views/**/*
            - app.js
            - officials-schedule-export.js
            - ecosystem-dev.json
            - deploy/**/*
            - .gitlab-ci.yml
            - Dockerfile
            - docker-compose-2.yml
    script:
        #todo: new log id
        - ansible-playbook -l marc-do-dev-sw-sw deploy/pm2_reload_dev.yml --extra-vars "SUFFIX=-2" -e "REDIS_URL=$REDIS_URL_DEV2" -e "HOST_PORT=$HOST_PORT_DEV2" -e "EMAIL_REDIS_URL=$EMAIL_REDIS_URL_DEV2" -e "SW_DB=$SW_DB_DEV2" -e "NODE_ENV=development" -e "LOG_PG_CS=$LOG_PG_CS_DEV" -e "LOG_APP_ID=$LOG_APP_ID"
    dependencies:
        - deploy:node_dev2
        - deploy:node_dev2_npm

deploy:static_dev2:
    stage: deploy
    only:
        refs:
            - development2
        changes:
            - bower.json
            - .bowerrc
            - webpack.config.base.js
            - webpack.config.frontend.js
            - assets/**/*
            - assets/**/**/*
            - frontend/**/*
            - frontend/**/**/*

    script:
        - |
          docker run --rm \
            -v `pwd`:/build \
            -w /build \
            -u $(id -u):$(id -g) \
            --env HOME=/build \
            --env NPM_CONFIG_CACHE=/build/.npm \
            --env NPM_CONFIG_PREFIX=/build/.npm-global \
            --env BOWER_CACHE=/build/.bower \
            --env EMAIL_DB=$EMAIL_DB_DEV \
            --env SW_DB=$SW_DB_DEV2 \
            --env REDIS_URL=$REDIS_URL_DEV2 \
            --env URLS=$URLS_DEV2_DO \
            --env ENV=development \
            node:22 \
            bash -c 'mkdir -p /build/.npm /build/.npm-global /build/.bower && npm install -g bower && npm ci && bower install --allow-root && npm run build:frontend'
        - ansible-playbook -l marc-do-dev-sw-sw deploy/static.yml --extra-vars "SUFFIX=-2"
    cache:
        key:
            files:
                - package-lock.json
                - bower.json
        paths:
            - node_modules/
            - bower_components/

deploy:static_events_dev2:
    stage: deploy
    only:
        refs:
            - development2
        changes:
            - bower.json
            - .bowerrc
            - webpack.config.base.js
            - webpack.config.frontend_event.js
            - assets/**/*
            - assets/**/**/*
            - frontend_event/**/*
            - frontend_event/**/**/*

    script:
        - |
          docker run --rm \
            -v `pwd`:/build \
            -w /build \
            -u $(id -u):$(id -g) \
            --env HOME=/build \
            --env NPM_CONFIG_CACHE=/build/.npm \
            --env NPM_CONFIG_PREFIX=/build/.npm-global \
            --env BOWER_CACHE=/build/.bower \
            --env SW_DB=$SW_DB_DEV2 \
            --env REDIS_URL=$REDIS_URL_DEV2 \
            --env URLS=$URLS_DEV2_DO \
            --env ENV=development \
            node:22 \
            bash -c 'mkdir -p /build/.npm /build/.npm-global /build/.bower && npm install -g bower && npm ci && bower install --allow-root && npm run build:event'
        - ansible-playbook -l marc-do-dev-sw-sw deploy/static_esw.yml --extra-vars "SUFFIX=-2"
    cache:
        key:
            files:
                - package-lock.json
                - bower.json
        paths:
            - node_modules/
            - bower_components/

deploy:static_admin_dev2:
    stage: deploy
    only:
        refs:
            - development2
        changes:
            - bower.json
            - .bowerrc
            - webpack.config.base.js
            - webpack.config.frontend_admin.js
            - assets/**/*
            - assets/**/**/*
            - frontend_admin/**/*
            - frontend_admin/**/**/*

    script:
        - |
          docker run --rm \
            -v `pwd`:/build \
            -w /build \
            -u $(id -u):$(id -g) \
            --env HOME=/build \
            --env NPM_CONFIG_CACHE=/build/.npm \
            --env NPM_CONFIG_PREFIX=/build/.npm-global \
            --env BOWER_CACHE=/build/.bower \
            --env SW_DB=$SW_DB_DEV2 \
            --env REDIS_URL=$REDIS_URL_DEV2 \
            --env URLS=$URLS_DEV2_DO \
            --env ENV=development \
            node:22 \
            bash -c 'mkdir -p /build/.npm /build/.npm-global /build/.bower && npm install -g bower && npm ci && bower install --allow-root && npm run build:admin'
        - ansible-playbook -l marc-do-dev-sw-sw deploy/static_asw_dev.yml --extra-vars "SUFFIX=-2"
    cache:
        key:
            files:
                - package-lock.json
                - bower.json
        paths:
            - node_modules/
            - bower_components/

deploy:doc_dev2:
    stage: deploy
    only:
        refs:
            - development2
        changes:
            - userconfig/**/*
            - apidoc.json
    script:
        - "docker run --rm -v `pwd`:/build -w /build -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 /bin/bash -c 'npm i --legacy-peer-deps && npm run doc;'"
        - ansible-playbook -l marc-do-dev-sw-sw deploy/doc_dev.yml --extra-vars "SUFFIX=-2"
    cache:
        key:
            files:
                - package-lock.json
                - bower.json
        paths:
            - node_modules/
            - bower_components/

