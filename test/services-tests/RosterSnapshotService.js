'use strict';
/*jslint maxlen: 500 */

const moment                = require('moment-timezone');

/**
 * Comments on the DB rows below: 
 *
 * There's an upcoming event.
 * There's one club ("master_club" row) that is registered to that event. This means there's 
 * a "roster_club" row for the club.
 * The club has 2 teams that are present on the event ("roster_team" rows). One of the team has a
 * locked roster ("locked" = TRUE). 
 * We didn't add "master_team" rows as this table is not required for the correct work of the 
 * methods under tests. 
 *
 * Both teams have 1 athlete ("master_athlete" and "roster_athlete" rows).
 */

const eventRow              = require('./fixture/RosterSnapshotService/event.row.json');
const divisionRows          = require('./fixture/RosterSnapshotService/division.rows.json');

const masterClubRow         = require('./fixture/RosterSnapshotService/master_club.row.json');
const rosterClubRow         = require('./fixture/RosterSnapshotService/roster_club.row.json');

const rosterTeamRows        = require('./fixture/RosterSnapshotService/roster_team.rows.json');
const masterTeamRows        = require('./fixture/RosterSnapshotService/master_team.rows.json');

const masterAthleteRow      = require('./fixture/RosterSnapshotService/master_athlete.rows.json');
const rosterAthleteRow      = require('./fixture/RosterSnapshotService/roster_athlete.rows.json');

const masterStaffRows       = require('./fixture/RosterSnapshotService/master_staff.rows.json');
const masterStaffRoleRows   = require('./fixture/RosterSnapshotService/master_staff_role.rows.json');
const rosterStaffRoleRows   = require('./fixture/RosterSnapshotService/roster_staff_role.rows.json');



let insert = (table, rows) => {
    let query = squel.insert({ autoQuoteFieldNames: true }).into(table);
    
    if (Array.isArray(rows)) {
        query.setFieldsRows(rows);
    } else {
        query.setFields(rows);
    }

    return Db.query(query);
}


function fillDB () {
    return Promise.all([
        (function insertEventRow () {
            // Roster deadline should be between now and "now + 10 minutes"
            eventRow.roster_deadline    = moment().tz(eventRow.timezone).add(3, 'minutes').format();

            // Set "not passed" event date end
            eventRow.date_end           = moment().tz(eventRow.timezone).add(1, 'month').format();

            return insert('event', eventRow);
        })(),
        (function insertDivisionRows () {
            let rosterDeadline = new Date();
            rosterDeadline.setMonth(rosterDeadline.getMonth() + 1);

            divisionRows[1].roster_deadline = rosterDeadline.toLocaleString('en-US');

            return insert('division', divisionRows)
        })(),
        insert('master_team', masterTeamRows),
        insert('roster_team', rosterTeamRows),
        insert('master_athlete', masterAthleteRow.map(a => {
            a.season = global.sails.config.sw_season.current;
            return a;
        })),
        insert('roster_athlete', rosterAthleteRow),
        insert('master_staff', masterStaffRows.map(s => {
            s.season = global.sails.config.sw_season.current;
            return s;
        })),
        insert('master_staff_role', masterStaffRoleRows),
        insert('roster_staff_role', rosterStaffRoleRows),

        insert('master_club', masterClubRow),
        (function insertRosterClub () {
            rosterClubRow.event_id = eventRow.event_id;
            return insert('roster_club', rosterClubRow)
        })()
        
    ])
}

function clearDB () {
    let queries = [
        'DELETE FROM "event"             WHERE "event_id"             = ' + eventRow.event_id, 
        `DELETE FROM "division"          WHERE "division_id"          IN (${divisionRows.map(d => d.division_id).join(', ')})`,
        `DELETE FROM "master_team"       WHERE "master_team_id"       IN (${masterTeamRows.map(t => t.master_team_id).join(', ')})`,
        `DELETE FROM "roster_team"       WHERE "roster_team_id"       IN (${rosterTeamRows.map(t => t.roster_team_id).join(', ')})`,
        `DELETE FROM "master_athlete"    WHERE "master_athlete_id"    IN (${masterAthleteRow.map(a => a.master_athlete_id).join(', ')})`,
        `DELETE FROM "roster_athlete"    WHERE "roster_athlete_id"    IN (${rosterAthleteRow.map(a => a.roster_athlete_id).join(', ')})`,
        `DELETE FROM "master_staff"      WHERE "master_staff_id"      IN (${masterStaffRows.map(s => s.master_staff_id).join(', ')})`,
        `DELETE FROM "master_staff_role" WHERE "master_staff_role_id" IN (${masterStaffRoleRows.map(r => r.master_staff_role_id).join(', ')})`,
        `DELETE FROM "roster_staff_role"`,
        `DELETE FROM "master_club"       WHERE "master_club_id"       = ` + masterClubRow.master_club_id,
        `DELETE FROM "roster_club"       WHERE "roster_club_id"       = ` + rosterClubRow.roster_club_id
    ];

    return Promise.all(
        queries.map(q => Db.query(q))
    ).then(() => {})
}

function clearMembersRows () {
    return Promise.all([
        Db.query('UPDATE "roster_athlete" SET "jersey" = NULL, "sport_position_id" = NULL'),
        Db.query('UPDATE "roster_staff_role" SET "primary" = NULL, "role_id" = NULL')
    ])
}

function getMembers () {
    return Promise.all([
        Db.query('SELECT roster_athlete_id FROM "roster_athlete" WHERE "jersey" IS NOT NULL AND "sport_position_id" IS NOT NULL'),
        Db.query('SELECT roster_staff_role_id FROM "roster_staff_role" WHERE "role_id" IS NOT NULL AND "primary" IS NOT NULL')
    ])
}

describe('RosterSnapshotService', function () {
    // TODO: not ready for development, need fixes
    if(process.env.TEST_NODE_ENV === 'development') {
        return;
    }

    let RosterSnapshotService;
    let currentSeason;
    let dbIds;

    before(() => {
        RosterSnapshotService   = global.sails.services.rostersnapshotservice;
        currentSeason           = global.sails.config.sw_season.current;

        return fillDB().then(data => {
            dbIds = data;
        })
    });

    after(() => clearDB());


    context('__copyMasterValuesForTeams()', function  () {

        it('should reject if season not passed', () => {
            return RosterSnapshotService.__copyMasterValuesForTeams({
                eventsList  : [],
                teamsList   : []
            }).should.be.rejectedWith({
                message: 'Invalid season'
            })
        })

        it('should reject if neither events nor teams passed', () => {
            return RosterSnapshotService.__copyMasterValuesForTeams({
                eventsList  : [],
                teamsList   : [],
                season      : currentSeason
            }).should.be.rejectedWith({
                message: `Either events' identifiers or teams' ones required`
            })
        })

        it('should reject if events list contains invalid identifier', () => {
            return RosterSnapshotService.__copyMasterValuesForTeams({
                eventsList  : [1, 2, 'test'],
                season      : currentSeason
            }).should.be.rejectedWith({
                message: 'All Items should be positive integers'
            })
        })

        it('should reject if teams list contains invalid identifier', () => {
            return RosterSnapshotService.__copyMasterValuesForTeams({
                teamsList   : [1, 2, 'test'],
                season      : currentSeason
            }).should.be.rejectedWith({
                message: 'All Items should be positive integers'
            })
        })

        context('', () => {

            after(() => clearMembersRows())

            it('should fill values for staff and athletes for specified event', () => {
                return RosterSnapshotService.__copyMasterValuesForTeams({
                    eventsList  : [eventRow.event_id],
                    season      : currentSeason
                }).then(data => {
                    expect(data).to.be.an('object');
                    expect(data.athletes).to.equal(1);
                    expect(data.staff).to.equal(1);
                }).then(() => {
                    return getMembers().then(res => {
                        let athletesCount = res[0].rows.length;
                        let staffCount = res[1].rows.length;

                        expect(athletesCount).to.equal(1);
                        expect(staffCount).to.equal(1);

                        let athleteID = res[0].rows[0].roster_athlete_id;
                        let staffID = res[1].rows[0].roster_staff_role_id;

                        expect(athleteID).to.equal(rosterAthleteRow[0].roster_athlete_id);
                        expect(staffID).to.equal(rosterStaffRoleRows[0].roster_staff_role_id);
                    })
                })
            })
        })

        context('', () => {

            after(() => clearMembersRows())

            it('should fill values for staff and athletes for specified event', () => {
                return RosterSnapshotService.__copyMasterValuesForTeams({
                    teamsList   : rosterTeamRows.map(t => t.roster_team_id),
                    season      : currentSeason
                }).then(data => {
                    expect(data).to.be.an('object');
                    expect(data.athletes).to.equal(1);
                    expect(data.staff).to.equal(1);
                }).then(() => {
                    return getMembers().then(res => {
                        let athletesCount = res[0].rows.length;
                        let staffCount = res[1].rows.length;

                        expect(athletesCount).to.equal(1);
                        expect(staffCount).to.equal(1);

                        let athleteID = res[0].rows[0].roster_athlete_id;
                        let staffID = res[1].rows[0].roster_staff_role_id;

                        expect(athleteID).to.equal(rosterAthleteRow[1].roster_athlete_id);
                        expect(staffID).to.equal(rosterStaffRoleRows[1].roster_staff_role_id);
                    })
                })
            })
        })
    })


    context('copyMasterValues()', function () {

        let __copyMasterValuesForTeamsSpy;

        before(() => {
            __copyMasterValuesForTeamsSpy = sinon.spy(RosterSnapshotService, '__copyMasterValuesForTeams');
        })

        after(() => {
            __copyMasterValuesForTeamsSpy.restore();
        })

        it('should copy master values to roster for not finished events', () => {
            return RosterSnapshotService.copyMasterValues(global.sails.config.sw_season.current)
            .then(data => {
                expect(data).to.be.an('object');
                expect(data.athletes).to.equal(1);
                expect(data.staff).to.equal(1);

                expect(__copyMasterValuesForTeamsSpy.calledOnce).to.be.true;

                let copyFnCall = __copyMasterValuesForTeamsSpy.firstCall;

                expect(copyFnCall.args).to.eql([{
                    eventsList: [eventRow.event_id],
                    season: global.sails.config.sw_season.current
                }]);
            })
        })

    })


    context('copyTeamsMembersValuesToRoster()', function () {
        let __copyMasterValuesForTeamsSpy;

        before(() => {
            __copyMasterValuesForTeamsSpy = sinon.spy(RosterSnapshotService, '__copyMasterValuesForTeams');
        })

        after(() => {
            __copyMasterValuesForTeamsSpy.restore();
        })
        
        it('should copy master values to roster for not finished events', () => {
            let teamsList   = rosterTeamRows.map(t => t.roster_team_id);
            let season      = global.sails.config.sw_season.current;

            return RosterSnapshotService.copyTeamsMembersValuesToRoster(
                teamsList, season
            ).then(data => {
                expect(data).to.be.an('object');
                expect(data.athletes).to.equal(1);
                expect(data.staff).to.equal(1);

                expect(__copyMasterValuesForTeamsSpy.calledOnce).to.be.true;

                let copyFnCall = __copyMasterValuesForTeamsSpy.firstCall;

                expect(copyFnCall.args).to.eql([{
                    teamsList, season
                }]);
            })
        })
    })



    /**
     * #upsertRosterAthletes() - this is the method that implements the master <-> roster 
     * relationship logic of athletes: when a CD changes teams roster on the club side (adds/removes
     *  an athlete), those changes should be applied to the teams on upcoming events also.
     *
     * The following tests check #__setAthletesQty() method also
     *
     * This context does not make full coverage
     */
    context('upsertRosterAthletes()', function () {

        let [{
            master_athlete_id   : masterAthleteID, 
            master_team_id      : masterTeamID,
            master_club_id      : masterClubID
        }] = masterAthleteRow;

        let rosterAthleteID = rosterAthleteRow[0].roster_athlete_id;

        let eventID = eventRow.event_id;

        let rosterTeamID = rosterTeamRows.filter(r => r.master_team_id === masterTeamID)[0].roster_team_id;


        let findAthletesCount = masterTeamID => 
            Db.query(
                `SELECT "roster_athletes_count" FROM "roster_team" WHERE "master_team_id" = $1`, 
                [masterTeamID]
            )
            .then(({ rows }) => rows[0] && rows[0].roster_athletes_count)
            .then(count => count == null ? null: count)

        let setInitialAthletesCount = masterTeamID =>
            Db.query(
                `UPDATE "roster_team" SET "roster_athletes_count" = 10 WHERE "master_team_id" = $1`,
                [masterTeamID]
            );

        let findAthleteOnEvent = (masterAthleteID, eventID) => 
            Db.query(
                `SELECT "roster_team_id", "roster_athlete_id" 
                 FROM "roster_athlete" 
                 WHERE "master_athlete_id" = $1 
                    AND "event_id" = $2 
                    AND "deleted" IS NULL 
                    AND "deleted_by_user" IS NULL`,
                [masterAthleteID, eventID]
            ).then(res => res.rows[0] || null);

        let changeTeamOfAthlete = (rosterTeamID, rosterAthleteID) => 
                Db.query(
                    `UPDATE "roster_athlete" SET "roster_team_id" = $1 WHERE "roster_athlete_id" = $2`,
                    [rosterTeamID, rosterAthleteID]
                );


        context('team change', () => {
            /**
             * Input data for the test: 
             *
             * We have 2 teams of the same club on the upcoming event. Each team has one athlete.
             * In our fixtures, one of the team is locked - so we cannot change members of this team. 
             * To check the athletes number recounting we unlock that team just for this test.
             *
             * Also, we move an athlete to another team to check he will be assigned 
             * back to the inital team during the test.
             */
            
            let { 
                roster_team_id: lockedRosterTeamID, 
                master_team_id: masterIDOfLockedRosterTeam
            } = rosterTeamRows.filter(r => r.locked)[0];

            let toggleTeamRosterLock = rosterTeamID => 
                Db.query(
                    `UPDATE "roster_team" SET "locked" = NOT("locked") WHERE "roster_team_id" = $1`, 
                    [rosterTeamID]
                );



            before(async () => {
                await toggleTeamRosterLock(lockedRosterTeamID);
                await changeTeamOfAthlete(lockedRosterTeamID, rosterAthleteID);
                await setInitialAthletesCount(masterTeamID);
                await setInitialAthletesCount(masterIDOfLockedRosterTeam)
            })

            after(() => toggleTeamRosterLock(lockedRosterTeamID))



            it('should move an athlete to another team and recount athletes number', () => {

                return RosterSnapshotService
                                .upsertRosterAthletes(masterClubID, masterAthleteID, masterTeamID)
                .then(() => findAthleteOnEvent(masterAthleteID, eventID))
                .then(athlete => {
                    expect(athlete).to.be.an('object');

                    expect(athlete.roster_team_id).to.equal(rosterTeamID);
                })
                .then(() => Promise.all([
                    findAthletesCount(masterTeamID),
                    findAthletesCount(masterIDOfLockedRosterTeam)
                ]))
                .then(([newTeamAthletesCount, oldTeamAthletesCount]) => {
                    expect(newTeamAthletesCount).to.equal(1);
                    expect(oldTeamAthletesCount).to.equal(1);
                })
            })
        })

        

        context('athlete removal', () => {
            /**
             * This is the case when an athlete was on the event. But evetually, his teams was 
             * removed from the event or he was moved to another team that is not present on the 
             * event.
             * 
             * Input data for the test: 
             * An athlete on event in a team that is no longer present on the event. 
             * This team is represented by a random id generated in the test below.
             */

            before(() => setInitialAthletesCount(masterTeamID))

            after(() => changeTeamOfAthlete(rosterTeamID, rosterAthleteID))



            it('should unassign an athlete from a team and recount athletes number', () => {
                // TODO: describe, why we make a random id
                let rndMasterTeamID = parseInt(Math.random() * 10, 10); 

                return RosterSnapshotService
                            .upsertRosterAthletes(masterClubID, masterAthleteID, rndMasterTeamID)
                .then(() => findAthleteOnEvent(masterAthleteID, eventID))
                .then(athlete => {
                    expect(athlete).to.be.an('object');

                    expect(athlete.roster_team_id).to.be.null;
                })
                .then(() => findAthletesCount(masterTeamID))
                .then(athletesCount => {
                    /* The athlete was removed and there's no athletes more in the team */
                    expect(athletesCount).to.equal(0);
                })
            })
        })



        context('athlete addition', () => {
            /**
             * This is the case when a CD adds an athlete to a club team. The team is present on an
             * event. But the athlete is not. 
             * So,the system should create a "roster_athlete" row for the athlete.
             *
             * Input data for the test:
             * As we have 2 athletes that are already on the event in our fixtures, we remove one
             * of them from the event expecting the system will add him back during the test.
             */

            let _newRosterAthleteID = null;

            let removeAthleteFromEvent = rosterAthleteID => 
                Db.query(
                    'DELETE FROM "roster_athlete" WHERE "roster_athlete_id" = $1', [rosterAthleteID]
                );
            
            

            before(() => Promise.all([
                removeAthleteFromEvent(rosterAthleteID),
                setInitialAthletesCount(masterTeamID)
            ]))

            after(() => Promise.all([
                removeAthleteFromEvent(_newRosterAthleteID), 
                insert('roster_athlete', 
                            rosterAthleteRow.filter(r => r.roster_athlete_id === rosterAthleteID))
            ]));



            it('should add an athlete to the team and recount athletes number', () => {
                return RosterSnapshotService
                                .upsertRosterAthletes(masterClubID, masterAthleteID, masterTeamID)
                .then(() => findAthleteOnEvent(masterAthleteID, eventID))
                .then(athlete => {
                    expect(athlete).to.be.an('object');

                    _newRosterAthleteID = athlete.roster_athlete_id;

                    expect(athlete.roster_team_id).to.equal(rosterTeamID);
                })
                .then(() => findAthletesCount(masterTeamID))
                .then(athletesCount => {
                    /* One athlete was added to the required team. And this is the only athlete there */
                    expect(athletesCount).to.equal(1);
                })
            })
        })

    });

    /**
     * #upsertRosterStaffRoles() - this is the method that implements the master <-> roster
     * relationship logic of staffers: when a CD changes team staff on the club side (adds/removes
     *  a staff), those changes should be applied to the teams on upcoming events also.
     *
     * The following tests check #__setStaffersQty() method also
     *
     * This context does not make full coverage
     */
    context('upsertRosterStaffRoles()', function () {
        let [{
            master_staff_id         : masterStaffID,
            master_team_id          : masterTeamID,
            master_club_id          : masterClubID
        }] = masterStaffRows;

        let rosterTeamID = rosterStaffRoleRows.filter(r => r.master_team_id === masterTeamID)[0].roster_team_id;

        let findStaffersCount = masterTeamID =>
            Db.query(
                `SELECT "roster_staff_count" FROM "roster_team" WHERE "master_team_id" = $1`,
                [masterTeamID]
            )
            .then(({ rows }) => rows[0] && rows[0].roster_staff_count)
            .then(count => count == null ? null : count)

        let setInitialStaffCount = masterTeamID =>
            Db.query(
                `UPDATE "roster_team" SET "roster_staff_count" = 10 WHERE "master_team_id" = $1`,
                [masterTeamID]
            );

        let findStaffOnEvent = (masterStaffID, rosterTeamID) =>
            Db.query(
                `SELECT * 
                 FROM "roster_staff_role" 
                 WHERE "master_staff_id" = $1 
                    AND "roster_team_id" = $2`,
                [masterStaffID, rosterTeamID]
            ).then(res => res.rows[0] || null);

        let addMasterStaffRole = (masterStaffID) =>
            Promise.all([
                Db.query(
                    `UPDATE "roster_staff_role" SET "deleted" = NULL WHERE "master_staff_id" = $1`,
                    [masterStaffID]
                ),
                insert('master_staff_role', [masterStaffRoleRows[0]])
            ]);

        let removeMasterStaffRole = (masterStaffID, masterTeamID) =>
            Db.query(
                `DELETE FROM "master_staff_role" WHERE "master_staff_id" = $1 AND "master_team_id" = $2`,
                [masterStaffID, masterTeamID]
            );

        let removeRosterStaffRole = (masterStaffID, masterTeamID) =>
            Db.query(
                `DELETE FROM "roster_staff_role" WHERE "master_staff_id" = $1 AND "master_team_id" = $2`,
                [masterStaffID, masterTeamID]
            );

        
        context('staff removal', () => {
            /**
             * This is the case when a staff was on the event. But eventually, his teams were
             * removed from the event or he was moved to another team that is not present on the
             * event.
             *
             * Input data for the test:
             * A staff on event in a team that is no longer present on the event.
             */

            before(() => 
                 Promise.all([
                    setInitialStaffCount(masterTeamID),
                    removeMasterStaffRole(masterStaffID, masterTeamID)
                ])
            );

            after(() => addMasterStaffRole(masterStaffID));

            it('should unassign a staff from a team and recount athletes number', async () => {
                await RosterSnapshotService.upsertRosterStaffRoles(masterClubID, masterStaffID);

                let [staffer, staffCount] = await findStafferAndQty();

                expect(staffer).to.be.an('object');
                expect(staffer.deleted).to.be.string;

                expect(staffCount).to.equal(0);
            })
        })

        let findStafferAndQty = () => Promise.all([
            findStaffOnEvent(masterStaffID, rosterTeamID),
            findStaffersCount(masterTeamID)
        ]);

        context('staff addition', () => {
            /**
             * 1. Add staff to team.
             * 2. Set team's staff count to 10.
             * 3. Upsert roster staff role.
             * 4. Check staff deleted field value.
             * 5. Check team's staff count.
             */

            let { master_team_id: masterTeamIDNew } = rosterTeamRows[1];

            let masterStaffIDNew = masterStaffRows.filter(r => 
                                        r.master_team_id === masterTeamIDNew)[0].master_staff_id;

            let addStafferToTeam = (masterStaffID, masterTeamID) =>
                Db.query(
                    `INSERT INTO "master_staff_role" (master_staff_id, master_team_id, role_id) VALUES($1, $2, 0)`,
                    [masterStaffID, masterTeamID]
                );

            before(() => Promise.all([
                addStafferToTeam(masterStaffIDNew, masterTeamID),
                setInitialStaffCount(masterTeamID)
            ]));

            after(() =>
                Promise.all([
                    removeMasterStaffRole(masterStaffIDNew, masterTeamID),
                    removeRosterStaffRole(masterStaffIDNew, masterTeamID)
                ])
            );

            it('should add a staff to team and recount staffers number', async () => {
                await RosterSnapshotService.upsertRosterStaffRoles(masterClubID, masterStaffIDNew)

                let staffer = await findStaffOnEvent(masterStaffIDNew, rosterTeamID);

                expect(staffer).to.be.an('object');
                expect(staffer.roster_team_id).to.equal(rosterTeamID);

                let count = await findStaffersCount(masterTeamID);

                expect(count).to.equal(2);
            })
        })

        context('staff restoring', () => {

            /**
             * 1. Delete master staff role field.
             * 2. Upsert roster staff role.
             * 3. Check roster staff count.
             * 4. Add staff to  master staff role.
             * 5. Upsert roster staff role.
             * 6. Check roster staff count.
             */
            it('should reinstate staff to an event', async () => {

                // removing staff from master team
                await removeMasterStaffRole(masterStaffID, masterTeamID);
                await RosterSnapshotService.upsertRosterStaffRoles(masterClubID, masterStaffID);

                let [staffer, staffCount] = await findStafferAndQty();

                expect(staffer).to.be.an('object');
                expect(staffer.deleted).to.be.string;
                expect(staffCount).to.equal(0);


                // adding staff from master team
                await insert('master_staff_role', [masterStaffRoleRows[0]]);
                await RosterSnapshotService.upsertRosterStaffRoles(masterClubID, masterStaffID);

                [staffer, staffCount] = await findStafferAndQty();

                expect(staffer).to.be.an('object');
                expect(staffer.deleted).to.be.null;
                expect(staffCount).to.equal(1);
            })

        })
    })
})
