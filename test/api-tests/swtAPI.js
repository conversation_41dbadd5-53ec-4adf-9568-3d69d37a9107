'use strict';

const
	request = require('request-promise'),
    moment  = require('moment-timezone');

function checkPurchaseRow (
	purchaseID, amount, ticketCode, scannerID, scannerLoc, zip,
	eventTickets, type, netProfit, collectedSWFee
) {
	let params = [
		purchaseID,
		amount,
		ticketCode,
		scannerID,
		scannerLoc,
		zip,
		eventTickets.length,
		type,
		netProfit
	];

	let sqlText =
		`SELECT
		 	p.purchase_id, 
		 	p.stripe_charge_id, 
		 	e.stripe_tickets_private_key "sk", 
		 	COUNT(pt.*) "pt_count",
		 	p."net_profit",
		 	p."collected_sw_fee"
		 FROM "purchase" p 
		 INNER JOIN "event" e 
		 	ON e.event_id = p.event_id
		 INNER JOIN "purchase_ticket" pt 
		 	ON pt.purchase_id = p.purchase_id 
		 	AND pt.event_ticket_id IN (${
		 		eventTickets.join(', ')
		 	})
		 WHERE p.purchase_id = $1
		 	AND p.amount = $2
		 	AND p.status = 'paid'
		 	AND p.payment_for = 'tickets'
		 	AND p.ticket_barcode = $3
		 	AND NULLIF(p.tickets_scan, '') IS NOT NULL 
		 	AND p.scanned_at <= NOW()
		 	AND p.scanner_id = $4
		 	AND p.scanner_location = $5
		 	AND p.payer_ip IS NOT NULL
		 	AND p.zip = $6
		 	AND p.source = 'api'
		 	AND p."type" = $8
		 	AND p."net_profit" = $9
		 	${
		 		(type === 'card')
		 			? ` AND NULLIF(p.stripe_card_fingerprint, '') IS NOT NULL
		 				AND p.stripe_payment_type = 'connect'
		 				AND p.type = 'card'
		 				AND p.stripe_charge_id IS NOT NULL 
		 				AND p.stripe_card_id IS NOT NULL 
		 				AND NULLIF(p.card_name, '') IS NOT NULL 
		 				AND NULLIF(p.card_last_4, '') IS NOT NULL `
		 			: ''
		 	}
		 GROUP BY p.purchase_id, e.event_id
		 HAVING COUNT(pt.*) = $7`;

	return Db.query(sqlText, params).then(sqlResult => {

		if(sqlResult.rowCount === 0) {
			throw new Error(JSON.stringify({
				message 	: 'Purchase row not found',
				sql 		: sqlText,
				params 		: params
			}));
		} else if(sqlResult.rowCount > 1) {
			throw new Error(JSON.stringify({
				message 	: 'Multiple purchase rows found',
				sql 		: sqlText,
				params 		: params
			}))
		}

		let row = sqlResult.rows[0];

		expect(Number(row.collected_sw_fee)).to.equal(collectedSWFee);
	})
}

describe('SWT API "api/controllers/API/Tickets/SWTApiController.js" (for TicketGuru App)', () => {
    let
        eventRow 			= require('./fixture/event.row.json'),
        eventTicketRows 	= require('./fixture/event.ticket.rows.json'),
        purchaseRow 		= require('./fixture/purchase.row.json'),
        purchaseTicketRow 	= require('./fixture/purchase_ticket.row.json');

    let scannerId 		= 'Test Scanner',
		scannerLocation = 'Test Gate',
        wristbandSerial = '123123',
		eventId,
		eventTickets,
		ticketBarcode,
        purchaseID;

	before(function insertTestDbRows () {
		eventRow.date_start = moment().tz(eventRow.timezone).add(1, 'minutes').format();
		eventRow.date_end = moment().tz(eventRow.timezone).add(2, 'hours').format();
		eventRow.tickets_purchase_date_start = moment().tz(eventRow.timezone).add(-1, 'minutes').format();
		eventRow.tickets_purchase_date_end = moment().tz(eventRow.timezone).add(30, 'minutes').format();

		return Db.query(
			squel.insert().into('event').setFields(eventRow).returning('event_id').toString()
		).then(function (sqlResult) {
			eventId = _.first(sqlResult.rows).event_id;

			let etr = eventTicketRows.map(function (row) {
				row.event_id = eventId;
				return row;
			});

			return Db.query(
				squel.insert().into('event_ticket').setFieldsRows(etr).returning('event_ticket_id').toString()
			).then(function (etResult) {
				eventTickets = etResult.rows.map(function (eventTicket) {
					return eventTicket.event_ticket_id;
				})
			})
		}).then(function () {
			purchaseRow.event_id = eventId;
			return Db.query(
				squel.insert().into('purchase')
					.setFields(purchaseRow).returning('purchase_id, ticket_barcode').toString()
			).then(function (sqlResult) {
				let purchase = _.first(sqlResult.rows);
                purchaseID    = purchase.purchase_id;
				ticketBarcode = purchase.ticket_barcode;
				return purchase;
			})
		}).then(function (purchase) {
			purchaseTicketRow.purchase_id = purchase.purchase_id;
			purchaseTicketRow.event_ticket_id = eventTickets[0];
			return Db.query(
				squel.insert().into('purchase_ticket').setFields(purchaseTicketRow).toString()
			)
		})
	})

	after(function removeTestDbRows () {
		return Promise.all([
			Db.query('DELETE FROM "event" WHERE "event_id" = $1', [eventId]),
			Db.query('DELETE FROM "event_ticket" WHERE "event_id" = $1', [eventId]),
			Db.query('DELETE FROM "purchase" WHERE "event_id" = $1', [eventId]),
			Db.query(`DELETE FROM "purchase_ticket" pt WHERE NOT EXISTS (
				SELECT p.purchase_id FROM "purchase" p
				WHERE p.purchase_id = pt.purchase_id
			)`),
			Db.query('DELETE FROM "request_log"')
		])
	})

	context('GET /api/swt/ping #ping()', () => {
		it('Should return "success": true', () => {
			return request({
				uri 	: `http://${HOST}/api/swt/ping`,
				json 	: true
			}).then(function (body) {
				body.should.have.property('success');
				body.success.should.be.equal(true);
			})
		})
	})

	context('POST /api/swt/lookup #lookup()', () => {

		let successCheck = function (respBody) {
			expect(respBody).to.be.a('object');

			respBody.should.have.property('success');
			respBody.success.should.be.equal(true);

			respBody.should.have.property('rows');
			respBody.rows.should.be.instanceof(Array).and.have.lengthOf(1);

            let foundItem = respBody.rows[0];
            foundItem.should.be.instanceof(Object);
            foundItem.should.have.property('event_id');
            foundItem.event_id.should.be.equal(eventId);
		}

		it('Should find tickets (exact search by last)', () => {
			return request({
				method 	: 'POST',
				uri 	: `http://${HOST}/api/swt/lookup`,
				body 	: {
					event_id 	: eventId,
					exact 		: 'true',
					last 		: 'Test'
				},
				json 	: true
			}).then(successCheck)
		})

		it('Should find tickets (exact search by zip)', () => {
			return request({
				method 	: 'POST',
				uri 	: `http://${HOST}/api/swt/lookup`,
				body 	: {
					event_id 	: eventId,
					exact 		: 'true',
					zip 		: '00000'
				},
				json 	: true
			}).then(successCheck)
		})

		it('Should find tickets (not exact search by last)', () => {
			return request({
				method 	: 'POST',
				uri 	: `http://${HOST}/api/swt/lookup`,
				body 	: {
					event_id 	: eventId,
					last 		: 'T'
				},
				json 	: true
			}).then(successCheck)
		})

		it('Should find tickets (not exact search by zip)', () => {
			return request({
				method 	: 'POST',
				uri 	: `http://${HOST}/api/swt/lookup`,
				body 	: {
					event_id 	: eventId,
					zip 		: '00'
				},
				json 	: true
			}).then(successCheck)
		})

		it('Should not find tickets (not exact search by zip)', () => {
			return request({
				method 	: 'POST',
				uri 	: `http://${HOST}/api/swt/lookup`,
				body 	: {
					event_id 	: eventId,
					zip 		: '123'
				},
				json 	: true
			}).then(function (respBody) {
				expect(respBody).to.be.a('object');

				respBody.should.have.property('success');
				respBody.success.should.be.equal(false);

				respBody.should.have.property('type');
				respBody.type.should.be.equal('not_found');

				respBody.should.have.property('message');
				respBody.message.should.be.equal(`Tickets not found for "Zip" = "123" (Not exact search)`);
			})
		})

        it('should accept array in "event_id" parameter', () => {
            return request({
                method  : 'POST',
                uri     : `http://${HOST}/api/swt/lookup`,
                body    : {
                    event_id    : [eventId, eventId + 10, '123456'],
                    zip         : '00'
                },
                json    : true
            }).then(successCheck)
        })
	})

	context('POST /api/swt/buy #buy()', () => {
		let uri 				= `http://${HOST}/api/swt/buy`,
			validCardNum 		= '****************',
			invalidCardNum 		= '****************',
			zip 				= '32054',
			bodyTickets;

		let onSuccess = function (respBody) {
			expect(respBody).to.be.a('object');

			respBody.should.have.property('success');
			respBody.success.should.be.equal(true);

			respBody.should.have.property('message');
			respBody.message.should.be.instanceof(String);

			respBody.should.have.property('id');
			respBody.id.should.be.instanceof(Number);

			respBody.should.have.property('ticket_code');
			respBody.id.should.be.instanceof(Number);

            respBody.should.have.property('tickets').with.lengthOf(1);

			return respBody;
		}

		before(function () {
			bodyTickets = JSON.stringify(_.map(eventTickets, ticketType => {
					return {
						id 		: ticketType,
						count 	: 1
					}
				})
			)
		})

		it('Should buy tickets for "connected" event (card)', async function () {

			this.timeout(20000);

            const e = await Db.query('SELECT (NOW() AT TIME ZONE e.timezone), e.tickets_purchase_date_end,  e.tickets_purchase_date_start from "event" e where event_id=$1', [eventId])
            console.log(e);

			return request({
				method 	: 'POST',
				uri 	: uri,
				body 	: {
					event_id 	: eventId,
					scanner  	: scannerId,
					location 	: scannerLocation,
					type 		: 'card',
					amount 		: 80,
					tickets 	: bodyTickets,
					card_num 	: validCardNum,
					card_month 	: '12',
					card_year 	: String(moment().add(2, 'years').year()),
					card_first 	: 'John',
					card_last 	: 'Smith',
					zip 		: zip
				},
				json 	: true
			}).then(onSuccess).then(respBody => {
				let netProfit = 74.38; // total - stripe fee - sw fee
				let swFee = 3

				return checkPurchaseRow(
					respBody.id, 80, respBody.ticket_code, scannerId,
					scannerLocation, zip, eventTickets, 'card', netProfit, swFee
				);
			}).catch(err => {
				if(typeof err !== 'string') {
					console.error(JSON.stringify(err, null, ' '))
				}
				throw err;
			})
		})

		it('Should buy tickets by cash', function () {
			this.timeout(3 * 1000);

			return request({
				method 		: 'POST',
				uri 		: uri,
				body 		: {
					event_id 	: eventId,
					scanner 	: scannerId,
					location 	: scannerLocation,
					type 		: 'cash',
					amount 		: 80,
					tickets 	: bodyTickets,
					user_first  : 'John',
					user_last  	: 'Smith',
					zip
				},
				json 		: true
			})
			.then(onSuccess)
			.then(respBody => {
				let netProfit = 77; // total - sw fee (2*1 + 1*1)
				let swFee = 3;

				return checkPurchaseRow(
					respBody.id, 80, respBody.ticket_code, scannerId, scannerLocation,
					zip, eventTickets, 'cash', netProfit, swFee
				);
			})
		})
	})

	it('POST /api/swt/test #test()', () => {

	})

	it('POST /api/swt/recent #recent()', () => {

	})

	context('POST /api/swt/scan #scan()', () => {
        let firstScanTimeMs = 0;
        it('Should scan ticket', () => {
            return Db.query(`UPDATE purchase SET wristband_serial = NULL WHERE purchase_id = $1`, [purchaseID])
                .then(() => {
                    return request({
                        method 	: 'POST',
                        uri 	: `http://${HOST}/api/swt/scan`,
                        body 	: {
                            event_id 	: eventId,
                            ticket 		: ticketBarcode,
                            scanner 	: scannerId,
                            location 	: scannerLocation
                        },
                        json 	: true
                    }).then(function (respBody) {
                        firstScanTimeMs = Date.now();
                        expect(respBody).to.be.a('object');

                        respBody.should.have.property('success');
                        respBody.should.have.property('ticket');

                        respBody.success.should.be.equal(true);
                        respBody.ticket.should.be.an.instanceOf(Object).and.have.property('code', ticketBarcode);
                    })
                })

        })

        it('Should return "not_exists"', () => {
            return request({
                method 	: 'POST',
                uri 	: `http://${HOST}/api/swt/scan`,
                body 	: {
                    event_id 	: eventId,
                    ticket 		: '111111111',
                    scanner 	: scannerId,
                    location 	: scannerLocation
                },
                json 	: true
            }).then(function (respBody) {
                expect(respBody).to.be.a('object');

                respBody.should.have.property('success');
                respBody.should.have.property('type');
                respBody.should.have.property('message');

                respBody.success.should.be.equal(false);
                respBody.type.should.be.equal('not_exists');
                respBody.message.should.be.equal('Ticket #111111111 does not exist');
            })
        })

        it('Should return "not_exists" for wristband serial', () => {
            return request({
                method 	: 'POST',
                uri 	: `http://${HOST}/api/swt/scan`,
                body 	: {
                    event_id 	: eventId,
                    ticket 		: wristbandSerial,
                    scanner 	: scannerId,
                    location 	: scannerLocation
                },
                json: true
            }).then(function (respBody) {
                expect(respBody).to.be.a('object');

                respBody.should.have.property('success');
                respBody.should.have.property('type');
                respBody.should.have.property('message');

                respBody.success.should.be.equal(false);
                respBody.type.should.be.equal('not_exists');
                respBody.message.should.be.equal('Ticket #123123 does not exist');
            })
        })

        it('Should return "already_scanned"', async () => {
            const cacheExpireTime = firstScanTimeMs + SWTAPIService.scan.RESULT_CACHE_TTL * 1000;
            const msUntilCacheExpired = Math.max(0, cacheExpireTime + 100 - Date.now());
            await new Promise(
                resolve => setTimeout(
                    resolve,
                    msUntilCacheExpired
                )
            );
			return request({
				method 	: 'POST',
				uri 	: `http://${HOST}/api/swt/scan`,
				body 	: {
					event_id 	: eventId,
					ticket 		: ticketBarcode,
					scanner 	: scannerId,
					location 	: scannerLocation
				},
				json 	: true
			}).then(function (respBody) {
				expect(respBody).to.be.a('object');

				respBody.should.have.property('success');
				respBody.success.should.be.equal(false);

				respBody.should.have.property('type');
				respBody.type.should.be.equal('already_scanned');

				respBody.should.have.property('message');
				respBody.message.should.be.equal(`Ticket #${ticketBarcode} already scanned or was not paid`);

				respBody.should.have.property('ticket');
				respBody.ticket.should.be.an.instanceOf(Object);
			})
		})

        it('Should scan wristband serial', () => {
            return Db.query(`UPDATE purchase SET wristband_serial = $2 WHERE purchase_id = $1`
                , [purchaseID, wristbandSerial]
            ).then(() => {
                return request({
                    method 	: 'POST',
                    uri 	: `http://${HOST}/api/swt/scan`,
                    body 	: {
                        event_id 	: eventId,
                        ticket 		: wristbandSerial,
                        scanner 	: scannerId,
                        location 	: scannerLocation
                    },
                    json: true
                }).then(function (respBody) {
                    expect(respBody).to.be.a('object');

                    respBody.should.have.property('success');
                    respBody.should.have.property('ticket');

                    respBody.success.should.be.equal(true);
                    respBody.ticket.should.be.an.instanceOf(Object).and.have.property('code', ticketBarcode);
                })
            })
        })

        it('Should scan wristband serial twice', () => {
            let sendRequest = request({
                method 	: 'POST',
                uri 	: `http://${HOST}/api/swt/scan`,
                body 	: {
                    event_id 	: eventId,
                    ticket 		: wristbandSerial,
                    scanner 	: scannerId,
                    location 	: scannerLocation
                },
                json: true
            });

            return Db.query(`UPDATE purchase SET wristband_serial = $2 WHERE purchase_id = $1`
                , [purchaseID, wristbandSerial]
            ).then(() => {
                return sendRequest.then(() => sendRequest).then(function (respBody) {
                    expect(respBody).to.be.a('object');

                    respBody.should.have.property('success');
                    respBody.should.have.property('ticket');

                    respBody.success.should.be.equal(true);
                    respBody.ticket.should.be.an.instanceOf(Object).and.have.property('code', ticketBarcode);
                })
            })
        })

        it('Should return "Invalid barcode format #12312"', () => {
            return request({
                method 	: 'POST',
                uri 	: `http://${HOST}/api/swt/scan`,
                body 	: {
                    event_id 	: eventId,
                    ticket 		: '12312',
                    scanner 	: scannerId,
                    location 	: scannerLocation
                },
                json: true
            }).then(function (respBody) {
                expect(respBody).to.be.a('object');

                respBody.should.have.property('success');
                respBody.should.have.property('type');
                respBody.should.have.property('message');

                respBody.success.should.be.equal(false);
                respBody.type.should.be.equal('not_exists');
                respBody.message.should.be.equal('Invalid barcode format #12312');
            })
        })
	})

    context('POST /api/swt/wristband #assignWristbandSerial()', () => {

        let wristbandSerial     = '123123';
        let wristbandAssignHost = `http://${HOST}/api/swt/wristband`;

        let resetWristbandSerialQuery   = `UPDATE purchase SET wristband_serial = NULL WHERE purchase_id = $1`;
        let setWristbandSerialQuery     = `UPDATE purchase SET wristband_serial = $2 WHERE purchase_id = $1`;
        let setTicketAvailableForScan   = `UPDATE purchase_ticket SET available = 0 WHERE purchase_id = $1`;
        let setTicketUnavailableForScan = `UPDATE purchase_ticket SET available = 1 WHERE purchase_id = $1`;

        before(() => {
            return Promise.all([
                Db.query(`UPDATE purchase_ticket SET available = 0 WHERE purchase_id = $1`, [purchaseID]),
                Db.query(`
                    UPDATE "event"
                    SET "tickets_settings" = 
                        COALESCE("tickets_settings", '{}'::JSONB) || '{"require_recipient_name_for_each_ticket": true}'
                    WHERE event_id = $1
                `, [eventId]),
            ])
        });

        after(() => {
            return Db.query(`
                    UPDATE "event"
                    SET "tickets_settings" = 
                        COALESCE("tickets_settings", '{}'::JSONB) || '{"require_recipient_name_for_each_ticket": false}'
                    WHERE event_id = $1
                `, [eventId]);
        });

        it('Should return "Reason required"', () => {
            return Db.query(setWristbandSerialQuery, [purchaseID, wristbandSerial]).then(() => {
                return request({
                    method 	: 'POST',
                    uri 	: wristbandAssignHost,
                    body 	: {
                        event_id 	: eventId,
                        ticket 		: ticketBarcode,
                        scanner 	: scannerId,
                        location 	: scannerLocation,
                        wristband   : wristbandSerial
                    },
                    json: true
                }).then(respBody => {
                    expect(respBody).to.be.a('object');

                    respBody.should.have.property('success');
                    respBody.should.have.property('type');
                    respBody.should.have.property('message');

                    respBody.success.should.be.equal(false);
                    respBody.type.should.be.equal('validation');
                    respBody.message.should.be.equal('Reason required');
                })
            })
        });

        it('Should assign wristband serial', () => {
            return Db.query(resetWristbandSerialQuery, [purchaseID]).then(() => {
                return request({
                    method 	: 'POST',
                    uri 	: wristbandAssignHost,
                    body 	: {
                        event_id 	: eventId,
                        ticket 		: ticketBarcode,
                        scanner 	: scannerId,
                        location 	: scannerLocation,
                        wristband   : wristbandSerial
                    },
                    json: true
                }).then(respBody => {
                    expect(respBody).to.be.a('object');

                    respBody.should.have.property('success');
                    respBody.should.have.property('ticket');

                    respBody.success.should.be.equal(true);
                    respBody.ticket.should.be.an.instanceOf(Object).and.have.property('code', ticketBarcode);
                })
            })
        })

        it('Should change wristband serial', () => {
            let newWristbandSerial = '111111';

            return Db.query(setWristbandSerialQuery, [purchaseID, wristbandSerial]).then(() => {
                return request({
                    method 	: 'POST',
                    uri 	: wristbandAssignHost,
                    body 	: {
                        event_id 	: eventId,
                        ticket 		: ticketBarcode,
                        scanner 	: scannerId,
                        location 	: scannerLocation,
                        wristband   : newWristbandSerial,
                        reason      : 'test reason'
                    },
                    json: true
                }).then(respBody => {
                    expect(respBody).to.be.a('object');

                    respBody.should.have.property('success');
                    respBody.should.have.property('ticket');

                    respBody.success.should.be.equal(true);
                    respBody.ticket.should.be.an.instanceOf(Object).and.have.property('code', ticketBarcode);
                })
            })
        })

        it('Should return "Ticket barcode required"', () => {
            return Db.query(setTicketAvailableForScan, [eventId]).then(() => {
                return request({
                    method 	: 'POST',
                    uri 	: wristbandAssignHost,
                    body 	: {
                        event_id 	: eventId,
                        ticket 		: 123123,
                        scanner 	: scannerId,
                        location 	: scannerLocation,
                        wristband   : wristbandSerial
                    },
                    json: true
                }).then(respBody => {
                    expect(respBody).to.be.a('object');

                    respBody.should.have.property('success');
                    respBody.should.have.property('type');
                    respBody.should.have.property('message');

                    respBody.success.should.be.equal(false);
                    respBody.type.should.be.equal('validation');
                    respBody.message.should.be.equal('Ticket barcode required');
                })
            })
        })

        it('Should return "Ticket #123123123 does not exist"', () => {
            return Db.query(setTicketAvailableForScan, [eventId]).then(() => {
                return request({
                    method 	: 'POST',
                    uri 	: wristbandAssignHost,
                    body 	: {
                        event_id 	: eventId,
                        ticket 		: 123123123,
                        scanner 	: scannerId,
                        location 	: scannerLocation,
                        wristband   : wristbandSerial
                    },
                    json: true
                }).then(respBody => {
                    expect(respBody).to.be.a('object');

                    respBody.should.have.property('success');
                    respBody.should.have.property('type');
                    respBody.should.have.property('message');

                    respBody.success.should.be.equal(false);
                    respBody.type.should.be.equal('not_exists');
                    respBody.message.should.be.equal('Ticket #123123123 does not exist');
                })
            })
        })

        it('Should return "Ticket is not scanned yet"', () => {
            return Db.query(setTicketUnavailableForScan, [purchaseID]).then(() => {
                return request({
                    method 	: 'POST',
                    uri 	: wristbandAssignHost,
                    body 	: {
                        event_id 	: eventId,
                        ticket 		: ticketBarcode,
                        scanner 	: scannerId,
                        location 	: scannerLocation,
                        wristband   : wristbandSerial
                    },
                    json: true
                }).then(respBody => {
                    expect(respBody).to.be.a('object');

                    respBody.should.have.property('success');
                    respBody.should.have.property('type');
                    respBody.should.have.property('message');

                    respBody.success.should.be.equal(false);
                    respBody.type.should.be.equal('validation');
                    respBody.message.should.be.equal('Ticket is not scanned yet');
                })
            })
        })


    })

	it('POST /api/swt/redeem #redeem()', () => {

	})

	it('GET /api/swt/events #events()', () => {

	})

	it('GET /api/swt/debug #debugQuery()', () => {

	})

	it('POST /api/swt/log_error #appErrorLogger()', () => {

	})
})
