const request = require('request-promise');
const moment  = require('moment-timezone');
const TicketRefundsService = require('../ticket-refunds/ticket-refunds.service');

const chargeStab = require('./fixture/success-chage.json');

const TICKET_CODE_REGEX = /^[0-9a-f]{32}\-[0-9]{9}\-[0-9a-f]{32}$/;

describe('POST /api/tickets/buy/camps', async function() {
    const uri = `http://${HOST}/api/tickets/buy/camps`;
    const email = '<EMAIL>';
    const phone = '10000000000';
    const first = 'Elvis';
    const last = 'Gross';
    const cardholder = {
        last: 'Diaz',
        first: 'Deborah',
    };
    const password = 'Pa$$w0rd!';
    const zip = '84101';
    const country = 'United States';
    const user_data = {
        email,
        phone,
        password,
        zip,
        country,
        first,
        last,
    };

    let events = {};
    let eventIds;
    let receipts;
    let chargeCreationStub;
    let StripeConnect;

    before(async function() {
        events.campBuyer = await createEvent(require('./fixture/event-camp-buyer'));
        events.campSeller = await createEvent(require('./fixture/event-camp-seller'));
        eventIds = _.values(events).map(({event_id}) => event_id);
        receipts = {
            notWaitlistedCampsBoughtByCheckWithSellerFeePayer: [
                {
                    camp_id: events.campSeller.campIdMap.get(128),
                    id: events.campSeller.ticketIdMap.get(1341),
                    price: 130,
                },
                {
                    camp_id: events.campSeller.campIdMap.get(130),
                    id: events.campSeller.ticketIdMap.get(1336),
                    price: 150,
                },
                {
                    camp_id: events.campSeller.campIdMap.get(133),
                    id: events.campSeller.ticketIdMap.get(1330),
                    price: 180,
                },
            ],
            notWaitlistedCampsBoughtByCardWithBuyerFeePayer: [
                {
                    camp_id: events.campBuyer.campIdMap.get(118),
                    id: events.campBuyer.ticketIdMap.get(1311),
                    price: 120,
                },
            ],
            notWaitlistedCampsBoughtByCheckWithBuyerFeePayer: [
                {
                    camp_id: events.campBuyer.campIdMap.get(119),
                    id: events.campBuyer.ticketIdMap.get(1312),
                    price: 130,
                },
                {
                    camp_id: events.campBuyer.campIdMap.get(121),
                    id: events.campBuyer.ticketIdMap.get(1314),
                    price: 150,
                },
                {
                    camp_id: events.campBuyer.campIdMap.get(124),
                    id: events.campBuyer.ticketIdMap.get(1317),
                    price: 180,
                },
            ],
            waitlistedCampsRegisteredByCheckWithBuyerFeePayer: [
                {
                    camp_id: events.campBuyer.campIdMap.get(119),
                    id: events.campBuyer.ticketIdMap.get(1320),
                    price: 100.33,
                },
                {
                    camp_id: events.campBuyer.campIdMap.get(123),
                    id: events.campBuyer.ticketIdMap.get(1324),
                    price: 100.77,
                },
            ],
            notWaitlistedCampsBoughtByCardWithSellerFeePayer: [
                {
                    camp_id: events.campSeller.campIdMap.get(127),
                    id: events.campSeller.ticketIdMap.get(1339),
                    price: 120,
                },
                {
                    camp_id: events.campSeller.campIdMap.get(130),
                    id: events.campSeller.ticketIdMap.get(1336),
                    price: 150,
                },
            ],
            waitlistedCampsRegisteredByCheckWithSellerFeePayer: [
                {
                    camp_id: events.campSeller.campIdMap.get(127),
                    id: events.campSeller.ticketIdMap.get(1340),
                    price: 100.22,
                },
                {
                    camp_id: events.campSeller.campIdMap.get(130),
                    id: events.campSeller.ticketIdMap.get(1335),
                    price: 100.55,
                },
            ],
            notWaitlistedCampWithInvalidPrice: [
                {
                    camp_id: events.campBuyer.campIdMap.get(118),
                    id: events.campBuyer.ticketIdMap.get(1311),
                    price: 100,
                },
            ],
            waitlistedCampWithSellerFeePayer: [
                {
                    camp_id: events.campSeller.campIdMap.get(127),
                    id: events.campSeller.ticketIdMap.get(1340),
                    price: 100.22,
                },
            ],
        };
    });

    after(() => removeTestDbRows(eventIds));

    beforeEach(() => {
        StripeConnect = require.cache[
            require.resolve('../../api/lib/StripeConnect')
            ].exports;

        chargeCreationStub = sinon.stub(StripeConnect, 'createClientPayment').resolves(chargeStab);
    })

    afterEach(async () => {
        chargeCreationStub.restore();
        await clearCardFingerprints(eventIds)
    });

    it('should successfully buy not waitlisted camp by card (buyer/buyer)', async function() {
        const receipt = receipts.notWaitlistedCampsBoughtByCardWithBuyerFeePayer;
        const {
            amount, stripe_fee, collected_sw_fee, net_profit
        } = TicketRefundsService.calculateResultsBuyer(
            {
                products: receipt.map((r) => ({
                    product_price: r.price,
                    discount: 0,
                    quantity: 1,
                    ticket_sw_fee: _.find(events.campBuyer.eventTickets, {event_ticket_id: r.id}).sw_fee,
                })),
                stripe_tickets_percent: events.campBuyer.stripe_tickets_percent,
            }
        );
        const respBody = await request({
            method: 'POST',
            uri,
            body: {
                token: 'tok_visa',
                receipt,
                user_data,
                cardholder,
                additional: {},
                event: events.campBuyer.ticketsCode,
                total: amount,
                method: 'card',
                payment_discount: 0,
            },
            json: true
        });
        expect(respBody).to.be.a('object');

        expect(respBody.is_donation).to.equal(false);
        const ticket_barcode = parseTicketBarcode(respBody.code);
        const purchase = await TicketRefundsService.getPaymentData({ticket_barcode});
        expect(purchase).to.be.object;
        expect(purchase.is_payment).to.be.true;
        expect(purchase.is_ticket).to.be.true;
        expect(purchase.linked_purchase_id).to.be.null;
        expect(purchase.amount).to.equal(amount);
        expect(purchase.event_id).to.equal(events.campBuyer.event_id);
        expect(purchase.status).to.equal('paid');
        expect(purchase.type).to.equal('card');
        expect(purchase.email).to.equal(email);
        expect(purchase.phone).to.equal(phone);
        expect(purchase.amount_refunded).to.be.null;
        expect(purchase.dispute_created).to.be.null;
        expect(purchase.payment_for).to.equal('tickets');
        expect(purchase.tickets_scan).to.be.null;
        expect(purchase.scanned_at).to.be.null;
        expect(purchase.scanner_id).to.be.null;
        expect(purchase.scanner_location).to.be.null;
        expect({first: purchase.first, last:purchase.last}).to.deep.equal(cardholder);
        expect(purchase.net_profit).to.equal(net_profit);
        expect(purchase.stripe_fee).to.equal(stripe_fee);
        expect(purchase.collected_sw_fee).to.equal(collected_sw_fee);
        expect(purchase.purchase_discount).to.equal(0);
        expect(purchase.stripe_percent).to.equal(2.9);
    }).timeout(10000);

    it('should successfully buy not waitlisted camp by check (buyer/buyer)', async function() {
        const receipt = receipts.notWaitlistedCampsBoughtByCheckWithBuyerFeePayer;
        const {
            amount, stripe_fee, collected_sw_fee, net_profit
        } = TicketRefundsService.calculateResultsBuyer(
            {
                products: receipt.map((r) => ({
                    product_price: r.price,
                    discount: 0,
                    quantity: 1,
                    ticket_sw_fee: _.find(events.campBuyer.eventTickets, {event_ticket_id: r.id}).sw_fee,
                })),
                stripe_tickets_percent: 0,
            }
        );
        const respBody = await request({
            method: 'POST',
            uri,
            body: {
                receipt,
                user_data,
                cardholder,
                additional: {},
                event: events.campBuyer.ticketsCode,
                total: amount,
                method: 'check',
                payment_discount: 0,
            },
            json: true,
        });
        expect(respBody).to.be.a('object');

        expect(respBody.is_donation).to.equal(false);
        const ticket_barcode = parseTicketBarcode(respBody.code);
        const purchase = await TicketRefundsService.getPaymentData({ticket_barcode});
        expect(purchase).to.be.object;
        expect(purchase.is_payment).to.be.true;
        expect(purchase.is_ticket).to.be.true;
        expect(purchase.linked_purchase_id).to.be.null;
        expect(purchase.amount).to.equal(amount);
        expect(purchase.event_id).to.equal(events.campBuyer.event_id);
        expect(purchase.status).to.equal('pending');
        expect(purchase.type).to.equal('check');
        expect(purchase.email).to.equal(email);
        expect(purchase.phone).to.equal(phone);
        expect(purchase.amount_refunded).to.be.null;
        expect(purchase.dispute_created).to.be.null;
        expect(purchase.payment_for).to.equal('tickets');
        expect(purchase.tickets_scan).to.be.null;
        expect(purchase.scanned_at).to.be.null;
        expect(purchase.scanner_id).to.be.null;
        expect(purchase.scanner_location).to.be.null;
        expect({first: purchase.first, last:purchase.last}).to.deep.equal(cardholder);
        expect(purchase.net_profit).to.equal(net_profit);
        expect(purchase.stripe_fee).to.equal(stripe_fee);
        expect(purchase.collected_sw_fee).to.equal(collected_sw_fee);
        expect(purchase.purchase_discount).to.equal(0);
        expect(purchase.stripe_percent).to.be.null;
    });

    it('should successfully register for waitlisted camp (buyer/buyer) ', async function() {
        const receipt = receipts.waitlistedCampsRegisteredByCheckWithBuyerFeePayer;
        const {
            amount, productResults
        } = TicketRefundsService.calculateResultsBuyer(
            {
                products: receipt.map((r) => ({
                    product_price: r.price,
                    discount: 0,
                    quantity: 1,
                    ticket_sw_fee: 0,
                })),
                stripe_tickets_percent: 0,
            }
        );
        const respBody = await request({
            method: 'POST',
            uri,
            body: {
                receipt,
                user_data,
                cardholder: {
                    last: null,
                    first: null,
                },
                additional: {},
                event: events.campBuyer.ticketsCode,
                total: amount,
                method: 'waitlist',
                payment_discount: 0
            },
            json: true,
        });
        expect(respBody).to.be.a('object');

        expect(respBody.is_donation).to.equal(false);
        expect(respBody.tickets).to.be.array;
        expect(respBody.tickets.length).to.equal(2);
        let purchase = await TicketRefundsService.getPaymentData({ticket_barcode: parseTicketBarcode(respBody.tickets[0].hash), canceled: true});
        expect(purchase).to.be.object;
        expect(purchase.is_payment).to.be.false;
        expect(purchase.is_ticket).to.be.true;
        expect(purchase.linked_purchase_id).to.be.null;
        expect(purchase.amount).to.equal(productResults[0].amount);
        expect(purchase.event_id).to.equal(events.campBuyer.event_id);
        expect(purchase.type).to.equal('waitlist');
        expect(purchase.email).to.equal(email);
        expect(purchase.phone).to.equal(phone);

        purchase = await TicketRefundsService.getPaymentData({ticket_barcode: parseTicketBarcode(respBody.tickets[1].hash), canceled: true});
        expect(purchase).to.be.object;
        expect(purchase.is_payment).to.be.false;
        expect(purchase.is_ticket).to.be.true;
        expect(purchase.linked_purchase_id).to.be.null;
        expect(purchase.amount).to.equal(productResults[1].amount);
        expect(purchase.event_id).to.equal(events.campBuyer.event_id);
        expect(purchase.type).to.equal('waitlist');
        expect(purchase.email).to.equal(email);
        expect(purchase.phone).to.equal(phone);
    }).timeout(10000);


    it('should successfully buy not waitlisted camp by card (seller/seller)', async function() {
        const receipt = receipts.notWaitlistedCampsBoughtByCardWithSellerFeePayer;
        const {
            amount, stripe_fee, collected_sw_fee, net_profit
        } = TicketRefundsService.calculateResults(
            {
                products: receipt.map((r) => ({
                    product_price: r.price,
                    discount: 0,
                    quantity: 1,
                    ticket_sw_fee: _.find(events.campSeller.eventTickets, {event_ticket_id: r.id}).sw_fee,
                })),
                stripe_tickets_percent: events.campSeller.stripe_tickets_percent,
            }
        );
        const respBody = await request({
            method: 'POST',
            uri,
            body: {
                token: 'tok_visa',
                receipt,
                user_data,
                cardholder,
                additional: {},
                event: events.campSeller.ticketsCode,
                total: amount,
                method: 'card',
                payment_discount: 0,
            },
            json: true
        });
        expect(respBody).to.be.a('object');

        expect(respBody.is_donation).to.equal(false);
        const ticket_barcode = parseTicketBarcode(respBody.code);
        const purchase = await TicketRefundsService.getPaymentData({ticket_barcode});
        expect(purchase).to.be.object;
        expect(purchase.is_payment).to.be.true;
        expect(purchase.is_ticket).to.be.true;
        expect(purchase.linked_purchase_id).to.be.null;
        expect(purchase.amount).to.equal(amount);
        expect(purchase.event_id).to.equal(events.campSeller.event_id);
        expect(purchase.status).to.equal('paid');
        expect(purchase.type).to.equal('card');
        expect(purchase.email).to.equal(email);
        expect(purchase.phone).to.equal(phone);
        expect(purchase.amount_refunded).to.be.null;
        expect(purchase.dispute_created).to.be.null;
        expect(purchase.payment_for).to.equal('tickets');
        expect(purchase.tickets_scan).to.be.null;
        expect(purchase.scanned_at).to.be.null;
        expect(purchase.scanner_id).to.be.null;
        expect(purchase.scanner_location).to.be.null;
        expect({first: purchase.first, last:purchase.last}).to.deep.equal(cardholder);
        expect(purchase.net_profit).to.equal(net_profit);
        expect(purchase.stripe_fee).to.equal(stripe_fee);
        expect(purchase.collected_sw_fee).to.equal(collected_sw_fee);
        expect(purchase.purchase_discount).to.equal(0);
        expect(purchase.stripe_percent).to.equal(events.campSeller.stripe_tickets_percent);
    }).timeout(10000);

    it('should successfully buy not waitlisted camp by check (seller/seller)', async function() {
        const receipt = receipts.notWaitlistedCampsBoughtByCheckWithSellerFeePayer;
        const {
            amount, stripe_fee, collected_sw_fee, net_profit
        } = TicketRefundsService.calculateResults(
            {
                products: receipt.map((r) => ({
                    product_price: r.price,
                    discount: 0,
                    quantity: 1,
                    ticket_sw_fee: _.find(events.campSeller.eventTickets, {event_ticket_id: r.id}).sw_fee,
                })),
                stripe_tickets_percent: 0,
            }
        );
        const respBody = await request({
            method: 'POST',
            uri,
            body: {
                receipt,
                user_data,
                cardholder,
                additional: {},
                event: events.campSeller.ticketsCode,
                total: amount,
                method: 'check',
                payment_discount: 0,
            },
            json: true,
        });
        expect(respBody).to.be.a('object');

        expect(respBody.is_donation).to.equal(false);
        const ticket_barcode = parseTicketBarcode(respBody.code);
        const purchase = await TicketRefundsService.getPaymentData({ticket_barcode});
        expect(purchase).to.be.object;
        expect(purchase.is_payment).to.be.true;
        expect(purchase.is_ticket).to.be.true;
        expect(purchase.linked_purchase_id).to.be.null;
        expect(purchase.amount).to.equal(amount);
        expect(purchase.event_id).to.equal(events.campSeller.event_id);
        expect(purchase.status).to.equal('pending');
        expect(purchase.type).to.equal('check');
        expect(purchase.email).to.equal(email);
        expect(purchase.phone).to.equal(phone);
        expect(purchase.amount_refunded).to.be.null;
        expect(purchase.dispute_created).to.be.null;
        expect(purchase.payment_for).to.equal('tickets');
        expect(purchase.tickets_scan).to.be.null;
        expect(purchase.scanned_at).to.be.null;
        expect(purchase.scanner_id).to.be.null;
        expect(purchase.scanner_location).to.be.null;
        expect({first: purchase.first, last:purchase.last}).to.deep.equal(cardholder);
        expect(purchase.net_profit).to.equal(net_profit);
        expect(purchase.stripe_fee).to.equal(stripe_fee);
        expect(purchase.collected_sw_fee).to.equal(collected_sw_fee);
        expect(purchase.purchase_discount).to.equal(0);
        expect(purchase.stripe_percent).to.be.null;
    });

    it('should successfully register for waitlisted camp (seller/seller)', async function() {
        const receipt = receipts.waitlistedCampsRegisteredByCheckWithSellerFeePayer;
        const {
            amount, productResults,
        } = TicketRefundsService.calculateResults(
            {
                products: receipt.map((r) => ({
                    product_price: r.price,
                    discount: 0,
                    quantity: 1,
                    ticket_sw_fee: _.find(events.campSeller.eventTickets, {event_ticket_id: r.id}).sw_fee,
                })),
                stripe_tickets_percent: events.campSeller.stripe_tickets_percent,
            }
        );
        const respBody = await request({
            method: 'POST',
            uri,
            body: {
                receipt,
                user_data,
                cardholder: {
                    last: null,
                    first: null,
                },
                additional: {},
                event: events.campSeller.ticketsCode,
                total: amount,
                method: 'waitlist',
                payment_discount: 0
            },
            json: true,
        });
        expect(respBody).to.be.a('object');

        expect(respBody.is_donation).to.equal(false);
        expect(respBody.tickets).to.be.array;
        expect(respBody.tickets.length).to.equal(2);
        let purchase = await TicketRefundsService.getPaymentData({ticket_barcode: parseTicketBarcode(respBody.tickets[0].hash), canceled: true});
        expect(purchase).to.be.object;
        expect(purchase.is_payment).to.be.false;
        expect(purchase.is_ticket).to.be.true;
        expect(purchase.linked_purchase_id).to.be.null;
        expect(purchase.amount).to.equal(productResults[0].amount);
        expect(purchase.event_id).to.equal(events.campSeller.event_id);
        expect(purchase.type).to.equal('waitlist');
        expect(purchase.email).to.equal(email);
        expect(purchase.phone).to.equal(phone);

        purchase = await TicketRefundsService.getPaymentData({ticket_barcode: parseTicketBarcode(respBody.tickets[1].hash), canceled: true});
        expect(purchase).to.be.object;
        expect(purchase.is_payment).to.be.false;
        expect(purchase.is_ticket).to.be.true;
        expect(purchase.linked_purchase_id).to.be.null;
        expect(purchase.amount).to.equal(productResults[1].amount);
        expect(purchase.event_id).to.equal(events.campSeller.event_id);
        expect(purchase.type).to.equal('waitlist');
        expect(purchase.email).to.equal(email);
        expect(purchase.phone).to.equal(phone);
    });

    it('should throw validation error when ticket price changed since opening ticket purchase page', async function() {
        const receipt = receipts.notWaitlistedCampWithInvalidPrice;
        const {
            amount
        } = TicketRefundsService.calculateResults(
            {
                products: receipt.map((r) => ({
                    product_price: r.price,
                    discount: 0,
                    quantity: 1,
                    ticket_sw_fee: _.find(events.campBuyer.eventTickets, {event_ticket_id: r.id}).sw_fee,
                })),
                stripe_tickets_percent: 0,
            }
        );
        const respBodyPromise = request({
            method: 'POST',
            uri,
            body: {
                token: 'tok_visa',
                receipt,
                user_data: {
                    ...user_data,
                    email: '<EMAIL>',
                },
                cardholder,
                additional: {},
                event: events.campBuyer.ticketsCode,
                total: amount,
                method: 'card',
                payment_discount: 0,
            },
            json: true
        });

        await expect(respBodyPromise)
            .to.eventually
            .be.rejectedWith('less than current price');
    });

    it('should throw validation error when trying to pay for waitlisted camp', async function() {
        const receipt = receipts.waitlistedCampWithSellerFeePayer;
        const {
            amount
        } = TicketRefundsService.calculateResults(
            {
                products: receipt.map((r) => ({
                    product_price: r.price,
                    discount: 0,
                    quantity: 1,
                    ticket_sw_fee: _.find(events.campSeller.eventTickets, {event_ticket_id: r.id}).sw_fee,
                })),
                stripe_tickets_percent: events.campSeller.stripe_tickets_percent,
            }
        );
        const respBodyPromise = request({
            method: 'POST',
            uri,
            body: {
                token: 'tok_visa',
                receipt,
                user_data: {
                    ...user_data,
                    email: '<EMAIL>',
                },
                cardholder,
                additional: {},
                event: events.campSeller.ticketsCode,
                total: amount,
                method: 'card',
                payment_discount: 0,
            },
            json: true
        });

        await expect(respBodyPromise)
            .to.eventually
            .be.rejectedWith('Not allowed to pay for Waitlisted Camps');
    });

    context('with not_require_sw_fee_for_checks set to true', async function() {
        let oldSettings;
        before(() => updateTicketSettings(events.campSeller.event_id, {not_require_sw_fee_for_checks: true}).then((s) => oldSettings = s));
        after(() => restoreSettings(events.campSeller.event_id, oldSettings));

        it('should successfully buy not waitlisted camp by check (seller/seller)', async function() {
            const receipt = receipts.notWaitlistedCampsBoughtByCheckWithSellerFeePayer;
            const {
                amount, stripe_fee, collected_sw_fee, net_profit
            } = TicketRefundsService.calculateResults(
                {
                    products: receipt.map((r) => ({
                        product_price: r.price,
                        discount: 0,
                        quantity: 1,
                        ticket_sw_fee: 0,
                    })),
                    stripe_tickets_percent: 0,
                }
            );
            const respBody = await request({
                method: 'POST',
                uri,
                body: {
                    receipt,
                    user_data: {
                        ...user_data,
                        email: '<EMAIL>',
                    },
                    cardholder,
                    additional: {},
                    event: events.campSeller.ticketsCode,
                    total: amount,
                    method: 'check',
                    payment_discount: 0,
                },
                json: true,
            });
            expect(respBody).to.be.a('object');

            expect(respBody.is_donation).to.equal(false);
            const ticket_barcode = parseTicketBarcode(respBody.code);
            const purchase = await TicketRefundsService.getPaymentData({ticket_barcode});
            expect(purchase).to.be.object;
            expect(purchase.amount).to.equal(amount);
            expect(purchase.net_profit).to.equal(net_profit);
            expect(purchase.stripe_fee).to.equal(stripe_fee);
            expect(purchase.collected_sw_fee).to.equal(collected_sw_fee);
            expect(purchase.purchase_discount).to.equal(0);
            expect(purchase.stripe_percent).to.be.null;
        });
    });

    async function createEvent(fixture) {
        const timeDiff = moment().diff(moment.tz(fixture.event.date_start, fixture.event.timezone));
        // change date as if event.date_start is NOW()
        const offsetDate = (date) => moment.tz(date, fixture.event.timezone).add(timeDiff, 'milliseconds').format();

        const {event_id, stripe_tickets_percent} = await createEventRow();
        const ticketsCode = fixture.event.event_tickets_code;
        const campIdMap = new Map();
        const eventCamps = await Promise.all(
            fixture.event_camp.map(createEventCampRow)
        );

        const ticketIdMap = new Map();
        const eventTickets = await Promise.all(
            fixture.event_ticket.map(createEventTicketRow)
        );

        return {
            event_id,
            stripe_tickets_percent,
            ticketsCode,
            eventCamps,
            campIdMap,
            eventTickets,
            ticketIdMap,
        };

        async function createEventRow () {
            const eventFields = {
                ...fixture.event,
                event_id: squel.str('DEFAULT'),
                date_start: offsetDate(fixture.event.date_start),
                date_end: offsetDate(fixture.event.date_end),
                tickets_purchase_date_start: offsetDate(fixture.event.tickets_purchase_date_start),
                tickets_purchase_date_end: offsetDate(fixture.event.tickets_purchase_date_end),
            };

            const result = await Db.query(
                squel.insert().into('event')
                    .setFields(eventFields)
                    .returning('event_id')
                    .returning('stripe_tickets_percent::FLOAT')
            );

            return result.rows[0];
        }

        async function createEventCampRow(camp) {
            const campFields = {
                ...camp,
                event_id,
                event_camp_id: squel.str('DEFAULT'),
                date_start: offsetDate(camp.date_start),
                date_end: offsetDate(camp.date_end),
            };

            const result = await Db.query(
                squel.insert().into('event_camp')
                    .setFields(campFields)
                    .returning('event_camp_id')
            );
            campIdMap.set(camp.event_camp_id, result.rows[0].event_camp_id);

            return result.rows[0].event_camp_id;
        }

        async function createEventTicketRow(eventTicket) {
            const eventTicketFields = {
                ...eventTicket,
                event_id,
                event_ticket_id: squel.str('DEFAULT'),
                event_camp_id: campIdMap.get(eventTicket.event_camp_id),
            };

            const swFeeColumn = squel.select()
                .field('COALESCE(NULLIF(event_ticket.application_fee, 0), e.tickets_sw_fee)')
                .from('event', 'e')
                .where('e.event_id = event_ticket.event_id')
                .limit(1)

            const result = await Db.query(
                squel.insert().into('event_ticket')
                    .setFields(eventTicketFields)
                    .returning('event_ticket_id')
                    .returning('application_fee')
                    .returning(squel.rstr('(?) "sw_fee"', swFeeColumn))
            );
            ticketIdMap.set(eventTicket.event_ticket_id, result.rows[0].event_ticket_id);

            return result.rows[0];
        }
    }
});

function parseTicketBarcode(code) {
    expect(code).to.match(TICKET_CODE_REGEX);
    return code.split('-')[1];
}

async function removeTestDbRows(eventIds) {
    await Db.query(
        squel.delete().from('purchase_ticket')
            .where(
                'purchase_id IN ?',
                squel.select().from('purchase', 'p')
                    .field('purchase_id')
                    .where('p.event_id IN ?', eventIds)
            )
    );
    const cleanupTables = ['event', 'event_ticket', 'event_camp', 'purchase'];
    await Promise.all(cleanupTables.map(table => Db.query(squel.delete().from(table).where('event_id IN ?', eventIds))));
}

async function clearCardFingerprints(eventIds) {
    await Db.query(
        squel.update().table('purchase')
            .set('stripe_card_fingerprint', null)
            .where('event_id IN ?', eventIds)
    );
}

async function updateTicketSettings(eventId, changes) {
    let oldSettings = await Db.query(
        squel.update().table('event')
            .set('tickets_settings', squel.str(`tickets_settings || ?`, JSON.stringify(changes)))
            .where('event_id = ?', eventId)
            .returning(
                squel.rstr(
                    '(?) "tickets_settings"',
                    squel.select()
                        .from('event')
                        .field('tickets_settings::TEXT')
                        .where('event_id = ?', eventId)
                )
            )
    );
    return oldSettings.rows[0].tickets_settings;
}

async function restoreSettings(eventId, oldSettings){
    await Db.query(
        squel.update().table('event')
            .set('tickets_settings', squel.rstr('?::JSONB', oldSettings))
            .where('event_id = ?', eventId)
    );
}
