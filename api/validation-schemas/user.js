const Joi = require('joi');
const { CA_ZIP_REG_EXP, BM_ZIP_REG_EXP, US_ZIP_REG_EXP, EMAIL_REGEX, CO_ZIP_REG_EXP } = require('../lib/joi-constants');
const { PL_COUNTRY_CODE, HN_COUNTRY_CODE, CO_COUNTRY_CODE, CA_COUNTRY_CODE, BM_COUNTRY_CODE } = require('../constants/common');

const base = Joi.object().keys({
    first:              Joi.string().required().label('First Name'),
    last:               Joi.string().required().label('Last Name'),
    country:            Joi.string().required().label('Country'),
    /*         country:            Joi.string()
                                    .valid('US', 'CA', 'BS', 'AS', 'BM', 'GU', 'ME', 'PR', 'VI', 'DO')
                                                                                .required().label('Country'), */
    email:              Joi.string().pattern(EMAIL_REGEX).required().label('Email Address'),
    gender:             Joi.string().valid('male', 'female', 'unspecified', 'non-binary').required().label('Gender'),
    phone_mob:          Joi.alternatives().conditional('country', {
        is: PL_COUNTRY_CODE,
        then: Joi.string().required().pattern(/^\d{9,}$/),
        otherwise: Joi.alternatives().conditional('country', {
            is: HN_COUNTRY_CODE,
            then: Joi.string().required().pattern(/^\d{8,}$/),
            otherwise: Joi.string().required().pattern(/^\d{10,}$/),
        }),
    }).label('Mobile Phone'),
    zip:                Joi.alternatives().conditional('country', {
        is :        CA_COUNTRY_CODE,
        then:       Joi.string().pattern(CA_ZIP_REG_EXP),
        otherwise: Joi.alternatives().conditional('country', {
            is: CO_COUNTRY_CODE,
            then: Joi.string().pattern(CO_ZIP_REG_EXP),
            otherwise: Joi.string().pattern(US_ZIP_REG_EXP),
        }),
    }).label('Zip Code'),
    role_club_director: Joi.boolean(),
    role_staff:         Joi.boolean(),
    role_sponsor:       Joi.boolean(),
    role_event_owner:   Joi.boolean(),
    password:           Joi.string().required().min(6).label('Password'),
    recognition_image: Joi.string().allow(null).optional().label('Recognition image'),
    recognition_verification_status: Joi.boolean().allow(null).optional().label('Recognition Verification Status')
});

const swtAppSignup = Joi.object({
    first: Joi.string().required().label('First Name'),
    last: Joi.string().required().label('Last Name'),
    country: Joi.string().required().label('Country'),
    email: Joi.string().email().required().label('Email Address'),
    phone_mob: Joi.alternatives().conditional('country', {
        is: PL_COUNTRY_CODE,
        then: Joi.string().required().pattern(/^\d{9,}$/),
        otherwise: Joi.alternatives().conditional('country', {
            is: HN_COUNTRY_CODE,
            then: Joi.string().required().pattern(/^\d{8,}$/),
            otherwise: Joi.string().required().pattern(/^\d{10,}$/),
        }),
    }).label('Mobile Phone'),
    password: Joi.string().required().min(6).label('Password'),
    zip: Joi.string()
        .when('country', {
            is: CA_COUNTRY_CODE,
            then: Joi.string().pattern(CA_ZIP_REG_EXP),
            otherwise: Joi.string().when('country', {
                is: BM_COUNTRY_CODE,
                then: Joi.string().pattern(BM_ZIP_REG_EXP),
                otherwise: Joi.string().when('country', {
                    is: CO_COUNTRY_CODE,
                    then: Joi.string().pattern(CO_ZIP_REG_EXP),
                    otherwise: Joi.string().pattern(US_ZIP_REG_EXP),
                }),
            }),
        })
        .label('Zip Code')
        .messages({
            'string.pattern.base': '"Zip Code" invalid format',
        }),
});

const updateV2 = Joi.object({
    type: Joi.string().valid('details', 'password', 'role'),
})
    .when(Joi.object({ type: Joi.string().valid('details') }).unknown(), {
        then: Joi.object({
            first: Joi.string().required().label('First Name'),
            last: Joi.string().required().label('Last Name'),
            country: Joi.string().required().label('Country'),
            email: Joi.string().email().required().label('Email Address'),
            gender: Joi.string()
                .valid('male', 'female', 'unspecified', 'non-binary')
                .required()
                .label('Gender'),
            phone_mob: Joi.alternatives().conditional('country', {
                is: PL_COUNTRY_CODE,
                then: Joi.string().required().pattern(/^\d{9,}$/),
                otherwise: Joi.alternatives().conditional('country', {
                    is: HN_COUNTRY_CODE,
                    then: Joi.string().required().pattern(/^\d{8,}$/),
                    otherwise: Joi.string().required().pattern(/^\d{10,}$/),
                }),
            }).label('Mobile Phone'),
            zip: Joi.string()
                .when('country', {
                    is: CA_COUNTRY_CODE,
                    then: Joi.string().pattern(CA_ZIP_REG_EXP),
                    otherwise: Joi.string().when('country', {
                        is: BM_COUNTRY_CODE,
                        then: Joi.string().pattern(BM_ZIP_REG_EXP),
                        otherwise: Joi.string().when('country', {
                            is: CO_COUNTRY_CODE,
                            then: Joi.string().pattern(CO_ZIP_REG_EXP),
                            otherwise: Joi.string().pattern(US_ZIP_REG_EXP),
                        }),
                    }),
                })
                .label('Zip Code'),
        }),
    })
    .when(Joi.object({ type: Joi.string().valid('password') }).unknown(), {
        then: Joi.object({
            password: Joi.string().required().min(6).label('Password'),
            new_password: Joi.string().optional().allow(null, '').min(6),
            password_confirmation: Joi.string()
                .optional()
                .allow(null, '')
                .valid(Joi.ref('new_password'))
                .preferences({
                    messages: {
                        any: {
                            allowOnly: 'Passwords do not match',
                        },
                    },
                }),
        }),
    })
    .when(Joi.object({ type: Joi.string().valid('role') }).unknown(), {
        then: Joi.object({
            role_club_director: Joi.boolean(),
            role_staff: Joi.boolean(),
            role_sponsor: Joi.boolean(),
            role_event_owner: Joi.boolean(),
        }),
    });

module.exports = {
    update: base.keys({
        new_password:       Joi.string().optional().allow(null, '').min(6),
        password_confirmation:  Joi.string().optional().allow(null, '').valid(Joi.ref('new_password')).preferences({
            messages: {
                any: {
                    allowOnly: 'Passwords do not match',
                }
            }
        }),
    }),
    updateV2,
    create: base.keys({
        password_confirmation:  Joi.string().required().valid(Joi.ref('password')).preferences({
            messages: {
                any: {
                    allowOnly: 'Passwords do not match',
                }
            }
        })
    }),
    password_recovery: Joi.object().keys({
        email: Joi.string().pattern(EMAIL_REGEX).required().label('Email Address'),
        isCreationMode: Joi.boolean().optional().label('Password recovery mode'),
    }),
    swtAppSignup,
};
