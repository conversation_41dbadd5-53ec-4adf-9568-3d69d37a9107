const QRTicketsGenerator = require('../../../lib/QRTicketsGenerator');

const DOMAIN_URL = sails.config.urls.home_page.baseUrl;
const APP_DOMAIN = sails.config.urls.main_app.baseUrl;
const APPLE_WALLET_ICON_PATH = sails.config.applePass.apple_wallet_icon;
const QR_CODE_URL_FORMAT = `${APP_DOMAIN}/images/qrcode/{0}.png`;

let generateTicketsLinks = function (data) {
    const tickets = data.tickets;

    let preparedData = tickets.map(ticket => {
        return {
            hash: generateReceiptLink(ticket, data.event_id),
            first: ticket.first,
            last: ticket.last
        }
    });

    return {
        raw: addTextToLinks(preparedData),
        formatted: addHTMLToLinks(preparedData)
    }
}

let getEventDatesInfo = function (data) {
    const eventDates = SWTReceiptService.getEventDatesInfo({
        event_date_start: data.event_date_start,
        event_date_end: data.event_date_end,
        city: data.event_city,
        state: data.event_state
    });

    return {
        raw: eventDates,
        formatted: eventDates
    }
}

let getAccountActivationLink = function (data) {
    const link = `${DOMAIN_URL}/activate-account/?email=${data.payer_email}`;

    return {
        raw: link,
        formatted: link
    };
}

let getEventLogo = function (data) {
    let logoURL = `${DOMAIN_URL}${data.event_logo}`;

    return {
        raw: null,
        formatted: eventLogoHTML(logoURL)
    }
}

let getQRCodeImage = function (data) {
    if(!data.ticket_barcode) {
        return {
            raw: '',
            formatted: ''
        }
    }

    let ticketPurchase = data.tickets.filter(t => Number(t.ticket_barcode) === (data.ticket_barcode));

    ticketPurchase = ticketPurchase[0];

    const QRCodeHash = QRTicketsGenerator.generateHash({
        ticket_barcode  : ticketPurchase.ticket_barcode,
        purchase_id     : ticketPurchase.purchase_id,
        user_id         : ticketPurchase.user_id,
        event_id        : data.event_id
    }, true);

    const QRCodeURL = QR_CODE_URL_FORMAT.format(QRCodeHash);

    return {
        formatted: QRCodeHTML(ticketPurchase.border_colour, QRCodeURL),
        raw: `<a href="${QRCodeURL}">${QRCodeURL}</a>`
    }
}

let getTicketsNamesList = function (data) {
    let ticketNames = data.tickets.map(ticket => ticket.ticket_type_name);

    let ticketTypeNames = ticketNames.join(', ');

    return {
        raw: ticketTypeNames,
        formatted: ticketTypeNames
    };
}

let additionalFields = function (data) {
    const tickets = data.tickets;
    const receiptFields = data.additional_fields || [];

    if (!receiptFields.length) {
        return {
            raw: '',
            formatted: ''
        };
    }

    const ticketsWithAdditionalFields = tickets.map(ticket => {
        const ticketAdditional = ticket.tickets_additional || {};

        const additionalFields = receiptFields.map(receiptField => {
            const value = ticketAdditional[receiptField.field] || '';
            const label = receiptField.label || receiptField.short_label || receiptField.field;

            return {
                label,
                value
            };
        });

        return {
            ...ticket,
            additional_fields: additionalFields.filter(field => field.value)
        };
    });

    return {
        raw: formatAdditionalFieldsText(ticketsWithAdditionalFields),
        formatted: formatAdditionalFieldsHTML(ticketsWithAdditionalFields)
    };
}

let getReceiptDescription = function (data) {
    return {
        raw: AEMService.stripHTML(data.tickets_receipt_descr),
        formatted: data.tickets_receipt_descr
    }
}

let getAppleWalletIcon = function (data) {
    if(data.is_apple_device) {
        let ticketPurchase = data.tickets.filter(t => Number(t.ticket_barcode) === (data.ticket_barcode));

        ticketPurchase = ticketPurchase[0];

        const appleWalletUrl = `${DOMAIN_URL}/api/pass/${data.event_id}/${ticketPurchase.purchase_id}`;
        const appleWalletIcon = APP_DOMAIN + APPLE_WALLET_ICON_PATH;

        return {
            formatted: appleWalletIconHTML(appleWalletUrl, appleWalletIcon),
            raw: `Add to Apple Wallet: ${appleWalletUrl}`
        }
    } else {
        return {
            formatted: '',
            raw: ''
        }
    }
}

const ACTIONS = {
    tickets_links: generateTicketsLinks,
    tickets_names_list: getTicketsNamesList,
    tickets_receipt_descr: getReceiptDescription,
    additional_fields: additionalFields,
    event_logo: getEventLogo,
    event_dates_info: getEventDatesInfo,
    qr_code_image: getQRCodeImage,
    apple_wallet_icon: getAppleWalletIcon,
    account_activation_link: getAccountActivationLink,
};

module.exports = function TicketsPaymentsVariablesCustomAction(variableObj, data) {
    let variableName = variableObj.field;

    let action = ACTIONS[variableName];

    if (action == null) {
        throw new Error(`Custom Action is not defined for variable '${variableName}'`);
    }

    return action(data);
}

function addTextToLinks (tickets) {
    return tickets.map(ticket => {
        return `${ticket.first} ${ticket.last} ${ticket.hash}`;
    })
}

function generateReceiptLink (ticket, eventID) {
    const formatHash = true;

    let receiptHash = QRTicketsGenerator.generateHash({
        ticket_barcode  : ticket.ticket_barcode,
        purchase_id     : ticket.purchase_id,
        user_id         : ticket.user_id,
        event_id        : eventID
    }, formatHash);

    return SWTReceiptService.getReceiptUrl(receiptHash);
}

function appleWalletIconHTML (appleWalletUrl, appleWalletIcon) {
    return `
        <div style="box-sizing: border-box;width: 100%;text-align: center;">
            <div style="box-sizing: border-box;">
              <a href="${appleWalletUrl}" target="_blank" style="box-sizing: border-box;color: #007bff;text-decoration: none;background-color: transparent;">
                  <img src="${appleWalletIcon}" alt="" style="box-sizing: border-box;vertical-align: middle;border-style: none;"/>
              </a>
            </div>
        </div>
    `;
}

function eventLogoHTML (eventLogo) {
    return `
        <div class="col num3" 
            style="display: table-cell; 
            vertical-align: middle; 
            max-width: 320px; 
            min-width: 159px; 
            width: 100%;">
            <div class="col_cont" style="width:100% !important;">
                <!--[if (!mso)&(!IE)]><!-->
                <div style="border-top:0px solid transparent; 
                    border-left:0px solid transparent; 
                    border-bottom:0px solid transparent; 
                    border-right:0px solid transparent; 
                    padding-top:5px; 
                    padding-bottom:5px; 
                    padding-right: 0px; 
                    padding-left: 0px;">
                    <!--<![endif]-->
                    <div class="img-container center autowidth" 
                        align="center" 
                        style="padding-right: 0px;padding-left: 0px;">
                        <!--[if mso]><table width="100%" 
                                            cellpadding="0" 
                                            cellspacing="0" 
                                            border="0">
                                            <tr style="line-height:0px">
                                                <td style="padding-right: 0px;padding-left: 0px;" align="center">
                        <![endif]-->
                            <img class="center autowidth" 
                                align="center" 
                                border="0" 
                                src="${eventLogo}" 
                                style="text-decoration: none; 
                                        -ms-interpolation-mode: bicubic; 
                                        height: auto; 
                                        border: 0; 
                                        width: 100%; 
                                        max-width: 100%; 
                                        display: block;" 
                                        width="160"
                            >
                        <!--[if mso]></td></tr></table><![endif]-->
                    </div>
                    <!--[if (!mso)&(!IE)]><!-->
                </div>
                <!--<![endif]-->
            </div>
        </div>
    `;
}

function QRCodeHTML (borderColour, qrUrl) {
    return `
        <div class="img-container center autowidth" align="center" style="padding-right: 0px;padding-left: 0px;">
            <!--[if mso]>
                <table width="100%" 
                       cellpadding="0" 
                       cellspacing="0" border="0">
                       <tr style="line-height:0px">
                           <td style="padding-right: 0px;padding-left: 0px;" align="center"><![endif]-->
                           <img class="center autowidth" 
                                align="center" 
                                src="${qrUrl}" 
                                width="213"
                                style="text-decoration: none; 
                                    -ms-interpolation-mode: bicubic; 
                                    height: auto;                                       
                                    width: 213px; 
                                    max-width: 100%; 
                                    display: block;                                  
                                    ${
                                        borderColour 
                                        ? `border: 15px #a5a4a4 solid !important; outline: 10px solid ${borderColour};` 
                                        : 'border: 0;' 
                                    }">
            <!--[if mso]></td></tr></table><![endif]-->
        </div>
    `;
}

function addHTMLToLinks (tickets) {
    return `
        <div style="box-sizing: border-box">
            <div style="box-sizing: border-box">
                ${tickets.map(ticket => {
                    return `
                        <a href="${ticket.hash}" target="_blank" class="btn receipt_ticket-holders_item" style="overflow: hidden; text-overflow: ellipsis; box-sizing: border-box;color: #337ab7;text-decoration: none;background-color: transparent;display: block;margin-bottom: 0;font-weight: bold;text-align: center;vertical-align: middle;cursor: pointer;background-image: none;border: solid 2px #337ab7 !important;white-space: nowrap;padding: 9px 0;font-size: 18px;line-height: 1.428571429;border-radius: 4px;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;max-width: 600px;margin: 0 auto 26px;">
                            <span style="box-sizing: border-box;">${ticket.first} ${ticket.last}</span>
                        </a>
                    `
                }).join(' ')}
            </div>
        </div>
       `;
}

function formatAdditionalFieldsText(tickets) {
    return tickets.map(ticket => {
        return ticket.additional_fields
            .map(field => `${field.label}: ${field.value}`)
            .join(', ');
    }).join('\n');
}

function formatAdditionalFieldsHTML(ticketsData) {
    return `
        <div style="box-sizing: border-box">
            ${ticketsData.map(ticket => {
                if (!ticket.additional_fields.some(field => field.value)) return '';
        
                return `
                    ${ticket.additional_fields.map(field => `
                        <p style="font-size:16px;line-height:120%;word-break:break-word">
                            <span style="font-size:16px;line-height:19px">
                                <code spellCheck="false">${field.label}:&nbsp;</code>
                                <code spellCheck="false">${field.value}</code>
                            </span>
                        </p>
                    `).join('')}
                `;
            }).join('')}
        </div>
    `;
}
