const RabbitMQConnectionManagerService = require('./RabbitMQConnectionManagerService');

class RabbitMQClient {
    /**
     * Publish a message to an exchange with a routing key.
     * If "message" is an object, we JSON-stringify it.
     */
    async publish(exchange, routingKey, message, options) {
        const channelWrapper = await RabbitMQConnectionManagerService.getChannel();

        let buffer;
        if(message instanceof Buffer) {
            buffer = message;
        } else if(message instanceof Uint8Array) {
            buffer = Buffer.from(message);
        } else if(message != null) {
            buffer = this.serializeMessage(message);
        } else {
            buffer = Buffer.alloc(0);
        }

        return channelWrapper.publish(exchange, routingKey, buffer, options);
    }

    serializeMessage(message) {
        return Buffer.from(JSON.stringify(message));
    }

    async close() {
        const channelWrapper = await RabbitMQConnectionManagerService.getChannel();

        await channelWrapper.cancelAll();

        await channelWrapper.close();

        loggers.debug_log.verbose('[RabbitMQClient] Channel closed');
    }
}

module.exports = new RabbitMQClient();
