'use strict';

const co = require('co');

const teamsConstants = require('../../constants/teams');
const ManualRosterAddition = require('./_ManualRosterAdditionService');
const RosterTeamService = require('../RosterTeamService');
const DivisionService = require('../DivisionService');
const { spawn } = require('child_process');
const path = require('path');

const VALIDATION_TYPES = {
    duplicates: Symbol('duplicates'),
    dbDuplicates: Symbol('dbDuplicates'),
};

class ManualTeamsAdditionService {
    constructor (RosterService) {
        this.RosterService = RosterService;
    }

    get importFile() {
        if(!this._importFile) {
            this._importFile = require('../../lib/FileStreamUploadService');
        }
        return this._importFile;
    }

    get S3_FOLDER() {
        return 'manualTeamsImport';
    }

    get IMPORTER_PATH() {
        return path.resolve(__dirname, '..', '..', '..', 'sw-utils');
    }

    get IMPORT_FILENAME() {
        return 'manual-teams-import.js';
    }

    async addTeamsManually(eventID, teamsData, {allowUpdating = false} = {}) {

        await this.__validateTeamNames(eventID, teamsData, [VALIDATION_TYPES.duplicates]);
        await this.__validateTeamCodes(eventID, teamsData, [VALIDATION_TYPES.duplicates]);

        let updatedTeamsData;
        if(allowUpdating) {
            const updatedTeams = await EventTeamService.manual_teams_addition.__pickTeamsForUpdate(eventID, teamsData);
            updatedTeamsData = {
                ...teamsData,
                teams: updatedTeams,
            };
        }
        await this.__validateTeamNames(eventID, teamsData, [VALIDATION_TYPES.dbDuplicates]);
        await this.__validateTeamCodes(eventID, teamsData, [VALIDATION_TYPES.dbDuplicates]);

        await this.__checkFreeCapacityOfDivisions(eventID, teamsData);

        let eventData = await EventTeamService.common.getEventData(eventID);

        if(_.isEmpty(eventData)) {
            throw { validation: 'Event not found' };
        }

        eventData.eventID = eventID;

        if(!eventData.is_with_manual_teams_addition) {
            throw { validation: 'Event doesn\'t support manual teams addition' };
        }

        let clubData = await EventTeamService.virtual_clubs.getVirtualClub(eventID);

        if(_.isEmpty(clubData) && !eventData.has_clubs) {
            clubData = await EventTeamService.virtual_clubs.createVirtualClub(eventID, eventData);
        } else if(eventData.has_clubs) {
            throw { validation: `Event has clubs. A virtual club cannot be created.` }
        }

        if(updatedTeamsData?.teams?.length > 0) {
            await this.__updateTeamsBulk(
                eventID,
                updatedTeamsData
            );
        }

        if(teamsData.teams.length > 0) {
            await this.__createTeamsBulk(clubData.master_club_id, clubData.roster_club_id, teamsData, eventData);
        }
    }

    async importTeams(eventID, statusEntry, file, getFileName) {
        let importFile;
        try {
            importFile = await this.importFile.uploadFile(file.stream, getFileName, this.S3_FOLDER);

            let importResult = await this.__runImportProcess(
                importFile.serverFilePath, eventID, statusEntry
            );

            return importResult;
        }
        finally {
            if(importFile) {
                importFile.removeServerFile().catch(err => loggers.errors_log.error(err));
            }
        }

    }

    validateImportRequestParams(statusEntry) {
        if(!teamsConstants.isValidEntryStatus(statusEntry)) {
            throw { validation: 'Invalid entry status' };
        }
    }

    /**
     * Finds rows in roster_teams by team_name and division_id from teamsData.teams,
     * removes found rows from this array and returns those rows as result with roster_team_id of existing row.
     *
     * @param eventID {number}
     * @param teamsData {Object}
     * @returns {Promise<Object[]>}
     */
    async __pickTeamsForUpdate(eventID, teamsData) {
        const dataTableColumns = [
            {name:'team_name'},
            {name:'division_id', type:'INTEGER'},
        ];
        const dataTableAlias = 'data';
        const {rows: existingRows} = await Db.query(
            knex('roster_team AS rt')
                .select([
                    'rt.team_name',
                    'rt.division_id',
                    'rt.roster_team_id',
                ])
                .join(
                    Db.utils.generateVirtualTableQuery(dataTableColumns, teamsData.teams, dataTableAlias),
                    {
                        [`${dataTableAlias}.team_name`]: 'rt.team_name',
                        [`${dataTableAlias}.division_id`]: 'rt.division_id',
                    },
                )
                .where('rt.event_id', eventID)
        );
        const foundTeamsMap = new Map();
        const getRowKey = (team) => JSON.stringify([team.team_name, team.division_id]);
        for(const team of existingRows) {
            foundTeamsMap.set(getRowKey(team), team);
        }
        const rowsToRemove = [];
        teamsData.teams.forEach((team, index) => {
            const key = getRowKey(team);
            const existingRow = foundTeamsMap.get(key);
            if(existingRow) {
                rowsToRemove.push(index);
                team.roster_team_id = existingRow.roster_team_id;
            }
        });

        return _.pullAt(teamsData.teams, rowsToRemove);
    }

    __runImportProcess(filePath, eventID, statusEntry) {
        return new Promise((resolve, reject) => {
            let result = [];
            let error = [];

            const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

            let procParams = [
                this.IMPORT_FILENAME,
                `--event=${eventID}`,
                `--statusEntry=${statusEntry}`,
                `--path=${filePath}`,
                `--conn=${Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64')}`,
            ];

            let proc = spawn('node', procParams, {
                cwd     : this.IMPORTER_PATH,
                stdio   : 'pipe'
            });

            const onProcessEnd = (code) => {
                if(code > 0) {
                    const errorString = Buffer.concat(error).toString();
                    try {
                        const parsedErrorString =  JSON.parse(errorString);

                        return parsedErrorString?.error ? reject(new Error(parsedErrorString.error)) : reject(parsedErrorString);
                    }
                    catch(err){
                        reject({error: errorString});
                    }
                } else {
                    try {
                        const scriptOutput = Buffer.concat(result).toString();
                        resolve(JSON.parse(scriptOutput));
                    } catch (err) {
                        reject(err);
                    }
                }
            };

            proc.on('error', (err) => reject(err));

            proc.on('close', onProcessEnd);

            proc.stdout.on('data', (data) => result.push(data));
            proc.stderr.on('data', (err) => error.push(err));
        })
    }

    __checkFieldDuplicates(teamsData, fieldName, fieldLabel) {
        const data = {};

        for (let team of teamsData.teams) {
            let fieldValue = team[fieldName].trim().toLowerCase();
            if (data[fieldValue]) throw { validation: `The ${fieldLabel} '${team[fieldName]}' is duplicated` };
            data[fieldValue] = true;
        }
    }

    async __checkExistedTeamFieldOfEvent (eventID, teamsData, fieldName, fieldLabel) {
        const data = teamsData.teams.map(team => team[fieldName]);

        const result = await RosterTeamService.checkTeamFieldsInDBUniqueness({ values: data, eventID, fieldName });

        if (result.length > 0) {
            let errorMessage;

            if (result.length === 1) {
                errorMessage = `The same ${fieldLabel} '${ result[0].value }' exists`;
            } else {
                const existedValues = result.map(res => res.value).join(', ');
                errorMessage = `The same ${fieldLabel} '${ existedValues }' exist`;
            }

            throw { validation: errorMessage };
        }
    }

    async __validateField(eventID, teamsData, validationTypes, fieldName, fieldLabel) {
        for(const validationType of validationTypes) {
            switch(validationType) {
                case VALIDATION_TYPES.duplicates: {
                    this.__checkFieldDuplicates(teamsData, fieldName, fieldLabel);
                    break;
                }
                case VALIDATION_TYPES.dbDuplicates: {
                    await this.__checkExistedTeamFieldOfEvent(eventID, teamsData, fieldName, fieldLabel);
                }
            }
        }
    }

    async __validateTeamNames (eventID, teamsData, validationTypes) {
        let fieldName = 'team_name';
        let fieldLabel = 'team name';

        await this.__validateField(eventID, teamsData, validationTypes, fieldName, fieldLabel);
    }

    async __validateTeamCodes (eventID, teamsData, validationTypes) {
        let teamsWithCodes = teamsData.teams.filter(team => !_.isEmpty(team) && team.team_code);

        if(!teamsWithCodes.length) {
            return;
        }

        let fieldName = 'team_code';
        let fieldLabel = 'team code';
        await this.__validateField(eventID, {teams: teamsWithCodes}, validationTypes, fieldName, fieldLabel);
    }
    
    async __checkFreeCapacityOfDivisions (eventID, { statusEntry, teams }) {
      let eventDivisions = {};
      
      for (let team of teams) {
        let divisionID = team.division_id;
                
        if (!eventDivisions[divisionID]) {
          eventDivisions[divisionID] = await DivisionService.getFreeDivisionCapacity({ eventID, divisionID, statusEntry });  
          eventDivisions[divisionID].volume = 0;          
        }

        eventDivisions[divisionID].volume += 1;
        
        if (eventDivisions[divisionID].freeCapacity < eventDivisions[divisionID].volume) {
          throw { validation: `The division '${eventDivisions[divisionID].name}' has not enough capacity` };
        }
      }
    }
    
    async __createTeamsBulk (masterClubID, rosterClubID, teamsData, eventData) {
        let tr = null;

        try {
            if(!masterClubID) {
                throw new Error('Master Club ID required');
            }

            if(!rosterClubID) {
                throw new Error('Roster Club ID required');
            }

            tr = await Db.begin();

            for(let team of teamsData.teams) {
                if(!team.team_name) {
                    throw { validation: 'Team name required' };
                }

                if(!team.division_id) {
                    throw { validation: 'Team division required' };
                }

                if(!eventData.divisions.includes(team.division_id)) {
                    throw { validation: 'Team division is not related to current event' };
                }

                let masterTeamID = await this.__createMasterTeamRow(tr, masterClubID, team, eventData);

                if(!masterTeamID) {
                    throw new Error('Master Team Not Created');
                }

                let statusEntry = teamsData.statusEntry || team.status_entry;

                if(statusEntry && !teamsConstants.isValidEntryStatus(statusEntry)) {
                    throw { validation: 'Team\'s entry status is not valid' };
                }

                let rosterTeamID = await this.__createRosterTeamRow(
                    tr, rosterClubID, masterTeamID, team, eventData, statusEntry
                );

                if(Array.isArray(team.members) && team.members.length) {
                    await this.RosterService.createTeamMembers(masterTeamID, rosterTeamID, team.members, tr);
                }
            }

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async __updateTeamsBulk(eventID, teamsData) {
        const dataTableColumns = [
            {name:'roster_team_id', type:'INTEGER'},
            {name:'team_code'},
        ];
        const dataTableAlias = 'data';
        const dataTableQuery = Db.utils.generateVirtualTableQuery(
            dataTableColumns,
            teamsData.teams,
            dataTableAlias
        );
        await Db.query(
            knex('roster_team AS rt')
                .update({
                    'team_code': knex.raw(`COALESCE(${dataTableAlias}.team_code, rt.team_code)`)
                })
                .toString() + `
                FROM ${dataTableQuery}
                WHERE ${dataTableAlias}.roster_team_id = rt.roster_team_id`
        );
    }

    __createMasterTeamRow (tr, masterClubID, teamData, eventData) {
        let query = squel.insert().into('master_team', 'mt')
            .set('team_name'            , teamData.team_name)
            .set('club_owner_id'        , EventTeamService.virtual_clubs.DEFAULTS.CD_ID)
            .set('sport_id'             , eventData.sport_id)
            .set('master_club_id'       , masterClubID)
            .set('sport_variation_id'   , eventData.sport_variation_id)
            .set('season'               , sails.config.sw_season.current)
            .returning('master_team_id');

        return tr.query(query).then(result => result.rows[0] && result.rows[0].master_team_id)
    }

    __createRosterTeamRow (
        tr, rosterClubID, masterTeamID, teamData, eventData, statusEntry = teamsConstants.ENTRY_STATUSES.ACCEPTED,
    ) {
        let query = squel.insert().into('roster_team')
            .set('team_name'            , teamData.team_name)
            .set('event_id'             , eventData.eventID)
            .set('division_id'          , teamData.division_id)
            .set('club_owner_id'        , EventTeamService.virtual_clubs.DEFAULTS.CD_ID)
            .set('sport_id'             , eventData.sport_id)
            .set('status_entry'         , statusEntry)
            .set('roster_club_id'       , rosterClubID)
            .set('status_paid'          , teamsConstants.PAYMENT_STATUSES.PAID)
            .set('master_team_id'       , masterTeamID)
            .set('date_entered'         , 'NOW()', { dontQuote: true })
            .set('date_completed'       , 'NOW()', { dontQuote: true })
            .set('date_paid'            , 'NOW()', { dontQuote: true })
            .set('date_accepted'        , this.__getAcceptedDate(statusEntry), { dontQuote: true })
            .set('division_name'        ,
                squel.select().from('division', 'd')
                    .field('d.name')
                    .where('d.division_id = ?', teamData.division_id)
            )
            .set('manual_club_name'     , teamData.manual_club_name ? teamData.manual_club_name.trim() : null)
            .set('team_code'            , teamData.team_code || null)
            .returning('roster_team_id')

        return tr.query(query).then(result => result && result.rows[0] && result.rows[0].roster_team_id);
    }

    __getAcceptedDate (entryStatus) {
        return Number(entryStatus) === teamsConstants.ENTRY_STATUSES.ACCEPTED ? 'NOW()' : null;
    }
}

module.exports = new ManualTeamsAdditionService(ManualRosterAddition);
