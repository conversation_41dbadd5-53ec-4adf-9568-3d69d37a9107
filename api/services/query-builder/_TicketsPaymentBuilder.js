const PaymentService = require("../PaymentService");

const PG_CURRENCY_FORMAT = 'FM$999,999,999,990D00';

class TicketsPaymentBuilder {
    constructor () {}

    emailReceiversList (eventID, receiversData) {
        if(!eventID) {
            throw new Error('Event ID required');
        }

        if(_.isEmpty(receiversData)) {
            throw new Error('Receivers Data is Empty');
        }

        // TODO: needs refactoring 
        const receiverEmail = receiversData.receiverEmail || null;

        let query = knex('event AS e')
            .select({
                event_date_start: 'e.date_start',
                event_date_end: 'e.date_end',
                event_city: 'e.city',
                event_state: 'e.state',
                event_logo: knex.raw(`
                    (
                        SELECT concat(em.file_path, '.', em.file_ext)
                        FROM event_media AS em
                        WHERE em.event_id IN (${HomePageService.DEFAULT_PLACEHOLDER_ID}, e.event_id)
                        AND em.file_type = 'main-logo'
                        ORDER BY em.event_id DESC
                        LIMIT 1 
                    )
                `),
                event_id: 'e.event_id',
                event_name: 'e.long_name',
                statement_decriptor: knex.raw(`
                    CASE 
                        WHEN e.tickets_payment_provider = ? THEN e.tickets_tilled_statement
                        ELSE e.tickets_stripe_statement
                    END
                `, [PaymentService.__PAYMENT_PROVIDERS__.TILLED]),
                purchase_id: 'payment_row.purchase_id',
                eo_email: 'u.email',
                is_apple_device: knex.raw('?::BOOLEAN', [!!receiversData.isAppleDevice]),
                tickets_receipt_descr: knex.raw(`COALESCE(e.tickets_receipt_descr, '')`),
                payer: knex.raw(`concat_ws(' ', payment_row.first, payment_row.last)`),
                service_fee: knex.raw(`TO_CHAR(payment_row.collected_sw_fee, ?)`, PG_CURRENCY_FORMAT),
                credit_card_merchant_fee: knex.raw(`TO_CHAR(payment_row.stripe_fee, ?)`, PG_CURRENCY_FORMAT),
                total_amount: knex.raw(`TO_CHAR(payment_row.amount, ?)`, PG_CURRENCY_FORMAT),
                additional_fields: knex.raw(`
                    COALESCE(              
                        (
                            SELECT ARRAY_TO_JSON(ARRAY_AGG("fields")) 
                            FROM JSONB_ARRAY_ELEMENTS(e.tickets_purchase_additional_fields) "fields"
                            WHERE ("fields"->'show_on'->>'receipt')::BOOLEAN IS TRUE
                        ), '[]'::JSON
                    )
                `),
                tickets: knex.raw(`(
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(tickets))), '[]'::JSON) 
                    FROM ( 
                        SELECT 
                            ticket.first, 
                            ticket.last, 
                            ticket.ticket_barcode, 
                            ticket.purchase_id, 
                            ticket.tickets_additional,
                            COALESCE(ticket.user_id, payment_row.user_id) user_id,
                            pt.border_colour,
                            et.label ticket_type_name
                        FROM purchase AS ticket
                        JOIN purchase_ticket AS pt ON pt.purchase_id = ticket.purchase_id
                        JOIN event_ticket AS et ON et.event_ticket_id = pt.event_ticket_id
                        WHERE COALESCE(ticket.linked_purchase_id, ticket.purchase_id) = payment_row.purchase_id
                            AND ticket.is_ticket IS TRUE AND ticket.event_id = e.event_id
                        ) tickets
                )`),
                payer_email: 'payment_row.email',
                payer_zip: 'payment_row.zip',
                payer_phone: knex.raw(`
                    regexp_replace(
                        (CASE
                            WHEN LENGTH(payment_row.phone) >= 10 THEN regexp_replace(payment_row.phone, '(\\D)', '', 'g')
                            ELSE payment_row.phone
                        END)
                    , '(\\d{3})(\\d{3})(\\d*)', '(\\1) \\2-\\3')
                `),
                payment_date_time: knex.raw(`TO_CHAR(payment_row.date_paid, 'Mon DD, YYYY, HH12:MI AM')`)
            })
            .join('purchase AS payment_row', (table) => {
                table.on('payment_row.event_id', 'e.event_id')
                    .andOn(knex.raw('payment_row.is_payment IS TRUE'))
                    .andOn(knex.raw(`payment_row.payment_for = 'tickets'`));

                if(receiversData.purchase_id) {
                    table.andOn(knex.raw(`payment_row.purchase_id = ?`, receiversData.purchase_id));
                }
            })
            .join('event_owner AS eo', 'eo.event_owner_id', 'e.event_owner_id')
            .join('user AS u', 'u.user_id', 'eo.user_id')
            .where('e.event_id', eventID);

        if(receiversData.ticket_barcode) {
            query.join(`purchase AS ticket_row`, (table) => {
                    table.on(knex.raw(
                        `COALESCE(ticket_row.linked_purchase_id, ticket_row.purchase_id) = payment_row.purchase_id`
                    ))
                        .andOn(knex.raw('ticket_row.is_ticket IS TRUE'))
                        .andOn(knex.raw(`ticket_row.ticket_barcode = ?`, receiversData.ticket_barcode))
                })
                .join('purchase_ticket AS pt', 'pt.purchase_id', 'ticket_row.purchase_id')
                .leftJoin('event_ticket AS et', 'pt.event_ticket_id', 'et.event_ticket_id')
                .select({
                    ticket_holder_name: knex.raw(`FORMAT('%s %s', ticket_row.first, ticket_row.last)`),
                    valid_dates: knex.raw(`(
                        SELECT COALESCE(ARRAY_AGG(TO_CHAR(TO_TIMESTAMP(vd, 'YYYY-MM-DD'), 'YYYY Dy, Mon DD')
                            ORDER BY (TO_TIMESTAMP(vd, 'YYYY-MM-DD')) ASC), '{All Days}')
                        FROM JSONB_OBJECT_KEYS(et.valid_dates) AS vd
                    )`),
                    ticket_barcode: 'ticket_row.ticket_barcode',
                    ticket_price: knex.raw(`TO_CHAR(ticket_row.amount, ?)`, PG_CURRENCY_FORMAT),
                    email: knex.raw(`COALESCE(?, "ticket_row".email, "payment_row"."email")`, receiverEmail),
                    to: knex.raw(`COALESCE(?, "ticket_row".email, "payment_row"."email")`, receiverEmail)
                })
        } else {
            query.select({
                email: 'payment_row.email',
                to: 'payment_row.email',
            })
        }
        return query;
    }
}

module.exports = new TicketsPaymentBuilder();
