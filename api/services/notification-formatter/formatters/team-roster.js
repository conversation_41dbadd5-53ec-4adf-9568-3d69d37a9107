
const {
    TEAM_ROSTER_STAFF_CHANGED_EO,
    TEAM_ROSTER_ATHLETE_CHANGED_EO,
    TEAM_MEMBER_ADDED,
    TEAM_MEMBER_REMOVED,
    TEAM_ROSTER_LOCKED_BY_DEADLINE,
    TEAM_ROSTER_LOCKED_BY_ONLINE_CHECKING,
    TEAM_ROSTER_UNLOCKED_BY_EVENT_OWNER,
    TEAM_ROSTER_LOCKED_BY_EO,
    TEAM_ROSTER_VALID_MARK_ADDED,
    TEAM_ROSTER_VALID_MARK_REMOVED,
    TEAM_ROSTER_STAFF_REMOVED_BY_SYSTEM,
    TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM,
} = require('../../../constants/notification-actions');

module.exports = {
    [TEAM_ROSTER_STAFF_CHANGED_EO]:
        (n) => {
            let changedData = __getChangedData(n.old_data, n.new_data);

            let title = `Team '${n.team_name}' changed by ${n.first} ${n.last}.`;

            if(changedData) {
                title += ` Staff: ${changedData.name}, changed: ${changedData.changes}`
            }

            return { title };
        },
    [TEAM_ROSTER_ATHLETE_CHANGED_EO]:
        (n) => {
            let changedData = __getChangedData(n.old_data, n.new_data);

            let title = `Team '${n.team_name}' changed by ${n.first} ${n.last}.`;

            if(changedData) {
                title += ` Athlete: ${changedData.name}, changed: ${changedData.changes}`
            }

            return { title };
        },
    [TEAM_MEMBER_ADDED]:
        (n) => ({
            title: `${n.comments} ${n.first ? `by ${n.first} ${n.last}` : ''}`,
            comments: ''
        }),
    [TEAM_MEMBER_REMOVED]:
        (n) => ({
            title: `${n.comments} ${n.first ? `by ${n.first} ${n.last}` : ''}`,
            comments: ''
        }),
    [TEAM_ROSTER_LOCKED_BY_DEADLINE]:
        (n) => ({
            title: `Team Roster ${n.team_name} is locked by Deadline`,
        }),
    [TEAM_ROSTER_LOCKED_BY_ONLINE_CHECKING]:
        (n) => ({
            title: `Team Roster ${n.team_name} is locked by Online Check In`,
        }),
    [TEAM_ROSTER_UNLOCKED_BY_EVENT_OWNER]:
        (n) => ({
            title: `Team Roster ${n.team_name} is unlocked by ${n.first} ${n.last}`,
        }),
    [TEAM_ROSTER_LOCKED_BY_EO]:
        (n) => ({
            title: `Team '${n.team_name}' Roster is locked by ${n.first} ${n.last}`,
        }),
    [TEAM_ROSTER_VALID_MARK_ADDED]:
        (n) => ({
            title: `Team '${n.team_name}' Roster is marked Valid by ${n.first} ${n.last}`,
        }),
    [TEAM_ROSTER_VALID_MARK_REMOVED]:
        (n) => ({
            title: `Team '${n.team_name}' Roster Valid mark removed by ${n.first} ${n.last}`,
        }),
    [TEAM_ROSTER_STAFF_REMOVED_BY_SYSTEM]:
        (n) => ({
            title: `Staff removed from Team '${n.team_name}' by System`,
        }),
    [TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM]:
        (n) => ({
            title: `Athlete removed from Team '${n.team_name}' by System`,
        }),
}

function __getChangedData(oldData, newData) {
    if(_.isEmpty(oldData)) {
        return null;
    }
    let changes = '';
    for (const key of Object.keys(oldData)) {
        if (__isEmptyValue(oldData[key]) && __isEmptyValue(newData[key])) {
            continue;
        }

        if(oldData[key] !== newData[key]) {
            changes += ` ${key}: ${oldData[key]} --> ${newData[key]}`
        }
    }

    return {
        name: oldData.name,
        changes: changes
    }
}

function __isEmptyValue(value) {
    return value === undefined || value === null || _.isNaN(value) ||
        (typeof value === 'object' && Object.keys(value).length === 0)
        || (typeof value === 'string' && value.trim().length === 0);
}
