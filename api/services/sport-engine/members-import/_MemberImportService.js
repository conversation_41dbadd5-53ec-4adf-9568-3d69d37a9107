const {
    staffImportSchema,
    athleteImportSchema,
} = require('../../../validation-schemas/member-import');
const UnresolvedMembershipService   = require('./_UnresolvedMembershipService');
const UnresolvedMembershipNotificationService = require("./_UnresolvedMembershipNotificationService");
const MemberTypeRulesService = require("./MemberTypeRulesService");
const MembersMapperService = require('./_MembersMapperService');

class MemberImportService {
    constructor(SportEngineUtils, swUtils) {
        /**
         * @type {SportEngineUtilsService}
         */
        this.Utils = SportEngineUtils;

        this.season = sails.config.sw_season.current;
        this._swUtils = swUtils;
    }

    get unresolvedMembership () {
        return new UnresolvedMembershipService(this.Utils)
    }
    get unresolvedMembershipNotification () {
        return new UnresolvedMembershipNotificationService()
    }

    get membersMapper () {
        return new MembersMapperService(this.Utils, this.season);
    }

    get MEMBER_TYPE_RULES_FIELDS () {
        return {
            FIELD_NAME: 'field_name',
        }
    }

    get MEMBER_TYPE_RULES_FIELD_NAMES () {
        return {
            MEMBER_DEFINITION_ID: 'membership_definition_id',
            AGE_GROUP_NAME: 'age_group_name',
        }
    }

    async #getMemberTypeRules() {
        const memberTypeRules = await MemberTypeRulesService.getMemberTypeRules();

        this.memberTypeRules = _.groupBy(memberTypeRules, this.MEMBER_TYPE_RULES_FIELDS.FIELD_NAME)
    }

    async groupMembers(members) {
        await this.#getMemberTypeRules();
        const membersWithErrors = [];
        const groupedMembers = _.chain(members)
            .groupBy(this.Utils.SE_FIELDS.ORGANIZATION_CODE)
            .mapValues(memberships => _.groupBy(memberships, (member) => {
                try {
                    return this.#getMemberType(member)
                } catch (error) {
                    loggers.errors_log.error(error.message, member);
                    membersWithErrors.push({...member, ...error})
                    return false;
                }
            }))
            .value();
        return {groupedMembers, membersWithErrors};
    }

    async createUnresolvedMemberships(memberships) {
        const createdUnresolvedMemberships = [];

        await Promise.all(this._swUtils.splitArray(memberships, 3).map(async (subArr) => {
            const membershipsList = await Promise.all(subArr.map(membership =>
                this.unresolvedMembership.createUnresolvedMembership(membership)
            )).catch((error) => loggers.errors_log.error(error));

            if(membershipsList && membershipsList.length) {
                createdUnresolvedMemberships.push(
                    ...membershipsList.filter(membership => !_.isEmpty(membership))
                );
            }
        }));

        return createdUnresolvedMemberships;
    }

    async sendUnresolvedMembershipNotifications(unresolvedMemberships) {
        return this.unresolvedMembershipNotification.sendUnresolvedMembershipNotifications(unresolvedMemberships);
    }

    prepareMember(member, memberType, rules, results) {
        if(_.isNaN(Number(member[this.Utils.SE_FIELDS.ORGANIZATION_CODE]))) {
            loggers.errors_log.error(`Invalid ${this.Utils.SE_FIELDS.ORGANIZATION_CODE} value`, member);

            return;
        }

        if (memberType === this.Utils.MEMBER_TYPE.ATHLETE) {
            const athleteFields = this.#prepareAthleteMember(member);

            if(!_.isEmpty(athleteFields)) {
                results.juniors.push(athleteFields);
            }
        } else {
            const staffFields = this.#prepareAdultMember(member, rules);

            if(!_.isEmpty(staffFields)) {
                results.adults.push(staffFields);
            }
        }
    }

    #prepareAthleteMember(member) {
        const athleteFields = this.membersMapper.athleteFieldsMapper(member);

        const validation = athleteImportSchema.validate(athleteFields);

        if (validation.error) {
            loggers.errors_log.error({
                validationErrors: validation.error.details,
                data: athleteFields,
            });

            return {};
        } else {
            return athleteFields;
        }
    }

    #prepareAdultMember(member, rules) {
        const staffFields = this.membersMapper.staffFieldsMapper(member, rules);

        const validation = staffImportSchema.validate(staffFields);

        if (validation.error) {
            loggers.errors_log.error({
                validationErrors: validation.error.details,
                data: staffFields,
            });

            return {};
        } else {
          return staffFields;
        }
    }

    prepareEmptyUsavFields(memberType) {
        const emptyUsavMember = {
            organization_code: null,
            usav_number: null,
            membership_status: null,
            safesport_statusid: null,
            safesport_start_date: null,
            safesport_end_date: null,
            sportengine_sync: null,
        };

        if (memberType === this.Utils.MEMBER_TYPE.ATHLETE) {
            const emptyUsavAthlete = {
                seasonality: null
            }
            return {...emptyUsavMember, ...emptyUsavAthlete};
        } else {
            const emptyUsavStaff = {
                is_impact: null,
                bg_screening: null,
                bg_expire_date: null,
            }
            return {...emptyUsavMember, ...emptyUsavStaff};
        }
    }

    #getMemberType(member) {
        const {member_type: memberType} = this.memberTypeRules[this.MEMBER_TYPE_RULES_FIELD_NAMES.MEMBER_DEFINITION_ID]
            ?.find(({rule_data}) => {
                return rule_data.includes(member[this.MEMBER_TYPE_RULES_FIELD_NAMES.MEMBER_DEFINITION_ID])
            }) || {};

        if (memberType) {
            return memberType;
        }

        const [ageGroupTag] = member.tags.filter(
            (tag) =>
                tag[this.Utils.TAG_FIELD.TYPE] === this.Utils.TAG_NAME.AGE_GROUP
        );

        let ageGroup;
        if (!_.isEmpty(ageGroupTag)) {
            ageGroup = ageGroupTag?.[this.Utils.TAG_FIELD.VALUE]?.value;
        } else {
            throw ({
                message: `No age group tag found for member ${
                    member[this.Utils.SE_FIELDS.MEMBERSHIP_STATUS]
                }
                (${member[this.Utils.SE_FIELDS.BIRTHDATE]})`
            });
        }

        const {member_type} = this.memberTypeRules[this.MEMBER_TYPE_RULES_FIELD_NAMES.AGE_GROUP_NAME]
            ?.find(({rule_data}) => {
                return rule_data.includes(ageGroup)
            }) || {};

        if (_.isEmpty(member_type)) {
            throw ({
                member_type: ageGroup,
                message: `New member type found:
                ${ageGroup} for member ${
                    member[this.Utils.SE_FIELDS.MEMBERSHIP_STATUS]
                }
                (${member[this.Utils.SE_FIELDS.BIRTHDATE]})`
            });
        }

        return member_type;
    }


}

module.exports = MemberImportService;
