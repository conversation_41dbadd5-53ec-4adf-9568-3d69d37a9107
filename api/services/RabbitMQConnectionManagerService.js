const { connect } = require('amqp-connection-manager');

class RabbitMQConnectionManagerService {
    constructor() {
        this.connection = null;
        this.channelWrapper = null;
    }

    get DEFAULT_CHANNEL_NAME() {
        return 'default';
    }

    async getConnection() {
        if(this.connection) {
            return this.connection;
        }

        try {
            const connection = connect(this.formatUri(sails.config.rabbitmq.uri));
            await connection.connect({ timeout: sails.config.rabbitmq.timeout });

            this.connection = connection;
        } catch(err) {
            console.error(`Failed to connect to a RabbitMQ broker ${sails.config.rabbitmq.uri}`, err);
            throw err;
        }

        return this.connection;
    }

    formatUri(uri) {
        return uri.split(',').map(x => x.trim());
    }

    async getChannel() {
        if(this.channelWrapper) {
            return this.channelWrapper;
        }

        if(!this.connection) {
            await this.getConnection();
        }

        const channelWrapper = this.connection.createChannel({
            name: this.DEFAULT_CHANNEL_NAME
        });

        channelWrapper.on('connect', () =>
            console.log(
                `Successfully connected a RabbitMQ channel "${this.DEFAULT_CHANNEL_NAME}"`
            )
        );

        channelWrapper.on('close', () =>
            console.log(`Successfully closed a RabbitMQ channel "${this.DEFAULT_CHANNEL_NAME}"`)
        );

        channelWrapper.on('error', (err) =>
            console.log(`Error on a RabbitMQ channel "${this.DEFAULT_CHANNEL_NAME}"`, err)
        );

        await channelWrapper.waitForConnect((err) => {
            if(err) {
                console.error(`Error connecting to channel ${this.DEFAULT_CHANNEL_NAME}`, err);
            }
        });

        this.channelWrapper = channelWrapper;

        return channelWrapper;
    }
}

module.exports = new RabbitMQConnectionManagerService();
