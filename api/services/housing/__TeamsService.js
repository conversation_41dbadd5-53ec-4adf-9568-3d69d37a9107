const { escapeStr, isNumberLessThanZero } = require("../../lib/swUtils");
const { ENTRY_STATUSES } = require('../../constants/teams');
const { THS_HOUSING_COMPANY_ID } = require('../../constants/housing');

const TEAM_FILTER_LABELS = {
    localDistance: 'localDistance',
    local: 'local',
    loyalty: 'loyalty',
    withoutDistance: 'withoutDistance',
};

const ORDER_BY_MAPPINGS = {
    'name': 'rt.team_name',
    'code': 'rt.organization_code',
    'division': 'd.name',
    'club': 'rc.club_name',
    'entered': 'rt.date_entered',
    'completed': '???',
    'paid': 'rt.date_paid',
    'accepted': 'rt.date_accepted',
    't': 'rt.total_tentative',
    'a': 'rt.total_accepted',
    'c': 'rt.total_confirmed',
    'm': 'rt.max_total_accepted',
    'es': 'rt.status_entry',
    'hs': 'rt.status_housing',
    'ps': 'rt.status_paid',
};

class TeamsService {
    async getEventTeams({ eventID, housingCompanyID, search, filterEntry, filterHousing, filterPayment, pagination, order, includeEventName }) {
        if (!eventID) {
            throw { validation: 'Invalid event identifier passed' };
        }

        const filters = {
            search: search ? escapeStr(search) : null,
            entry: filterEntry ? filterEntry.split(',') : null,
            payment: filterPayment ? filterPayment.split(',') : null,
            housing: this._parseHousingFilters(filterHousing)
        };

        const { teams, totalRows, eventName } = await this._getTeams({ eventID, housingCompanyID, filters, pagination, order, includeEventName });

        return {
            teams,
            total_rows: totalRows,
            ...(includeEventName && { event_name: eventName })
        };
    }

    _parseHousingFilters(filterHousing) {
        if (!filterHousing) return null;

        const filters = filterHousing.split(',');
        return {
            codes: filters.filter(item => Number(item)),
            labels: filters.filter(item => !Number(item))
        };
    }

    async _getTeams({ eventID, housingCompanyID, filters, pagination, order, includeEventName }) {
        const query = this._buildTeamsQuery({
            eventID,
            housingCompanyID,
            filters,
            pagination,
            order,
            includeEventName
        });

        const result = await Db.query(query);

        const totalRows = result.rows[0]?.total_count || 0;
        const eventName = includeEventName ? (result.rows[0]?.event_name || null) : null;

        return {
            teams: result.rows.map(({ total_count, event_name, ...team }) => team),
            totalRows,
            eventName
        };
    }

    _buildTeamsQuery({ eventID, housingCompanyID, filters, pagination, order, includeEventName }) {
        const selectFields = [
            ...this._getSelectFields(housingCompanyID),
            knex.raw('COUNT(*) OVER()::INT as total_count')
        ];

        if (includeEventName) {
            selectFields.push('e.long_name as event_name');
        }

        const query = knex
            .select(selectFields)
            .from('roster_team AS rt')
            .leftJoin('division AS d', 'd.division_id', 'rt.division_id')
            .leftJoin('roster_club AS rc', 'rc.roster_club_id', 'rt.roster_club_id')
            .innerJoin('event AS e', function() {
                return this.on(knex.raw(`e.event_id = rt.event_id AND e.housing_company_id = ?`, [housingCompanyID]));
            })
            .whereRaw(`rt.deleted IS NULL AND rt.event_id = ?`, [eventID]);

        this._applyFilters(query, filters);

        if (order) {
            const [orderField, orderDirection] = order.split(',');
            if (ORDER_BY_MAPPINGS[orderField]) {
                query.orderByRaw(`
                    ${ORDER_BY_MAPPINGS[orderField]} ${orderDirection === 'true' ? 'ASC' : 'DESC'} NULLS LAST
                `);
            }
        }

        const { limit, page } = pagination || {};
        if (!isNumberLessThanZero(limit) && limit > 0) {
            query.limit(limit);
            if (!isNumberLessThanZero(page) && page > 0) {
                query.offset(page * limit);
            }
        }

        return query;
    }

    _getSelectFields(housingCompanyID) {
        const baseFields = [
            'rt.roster_team_id',
            'rt.team_name',
            'rt.organization_code',
            'rt.roster_club_id',
            'rt.date_completed',
            'rt.status_entry',
            'rt.status_housing',
            'rt.status_paid',
            'rt.status_roster',
            'rc.is_local AS is_local_club',
            'rc.club_name',
            'd.short_name AS division',
            knex.raw(`TO_CHAR(rt.housing_status_changed_at::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM') as housing_status_changed_at`),
            knex.raw(`TO_CHAR(rt.date_paid::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM') as date_paid`),
            knex.raw(`
                        (TO_CHAR((
                            CASE
                            WHEN rt.status_entry = ?
                            THEN rt.date_accepted
                            WHEN rt.status_entry = ?
                            THEN COALESCE(
                                (SELECT MAX(ec.created)
                                FROM "event_change" ec
                                WHERE ec.roster_team_id = rt.roster_team_id AND ec.event_id = rt.event_id
                                        AND ec.action = 'team.entry.pending'
                                ), rt.date_entered
                            )
                            WHEN rt.status_entry = ?
                            THEN (SELECT MAX(ec.created)
                                    FROM "event_change" ec
                                    WHERE ec.roster_team_id = rt.roster_team_id AND ec.event_id = rt.event_id
                                        AND ec.action = 'team.entry.declined'
                            )
                            WHEN rt.status_entry = ?
                            THEN (SELECT MAX(ec.created)
                                    FROM "event_change" ec
                                    WHERE ec.roster_team_id = rt.roster_team_id AND ec.event_id = rt.event_id
                                        AND ec.action = 'team.entry.waiting'
                            )
                            END
                        ) :: TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM')) as status_date
                    `, [ENTRY_STATUSES.ACCEPTED, ENTRY_STATUSES.PENDING, ENTRY_STATUSES.DECLINED, ENTRY_STATUSES.WAITLIST])
        ];

        if (housingCompanyID === THS_HOUSING_COMPANY_ID) {
            return [
                ...baseFields,
                knex.raw(`coalesce(rt.total_tentative::text, '-') as total_tentative`),
                knex.raw(`coalesce(rt.total_confirmed::text, '-') as total_confirmed`),
                knex.raw(`coalesce(rt.total_accepted::text, '-') as total_accepted`),
                knex.raw(`coalesce(rt.max_total_accepted::text, '-') as max_total_accepted`)
            ];
        }

        return baseFields;
    }

    _applyFilters(query, filters) {
        if (!filters) return;

        const { search, entry, housing, payment } = filters;

        if (search) {
            const searchFormatted = `%${search}%`;
            query.whereRaw(`(
                rt.team_name ILIKE ?
                OR rc.club_name ILIKE ?
                OR rt.organization_code ILIKE ?
                OR d.name ILIKE ?
            )`, [searchFormatted, searchFormatted, searchFormatted, searchFormatted]);
        }

        if (Array.isArray(entry) && entry.length) {
            query.whereIn('rt.status_entry', filters.entry);
        }

        if (housing) {
            const { labels, codes } = housing;

            if (labels?.includes(TEAM_FILTER_LABELS.localDistance)) {
                query.whereRaw('rc.distance_to_event <= e.housing_local_teams_distance');
                query.whereRaw('rc.is_local IS NOT TRUE');
            }

            if (labels?.includes(TEAM_FILTER_LABELS.local)) {
                query.where('rc.is_local', true);
            }

            if (labels?.includes(TEAM_FILTER_LABELS.loyalty)) {
                query.where('rt.ths_loyalty', THS_HOUSING_COMPANY_ID);
            }

            if (labels?.includes(TEAM_FILTER_LABELS.withoutDistance)) {
                query.whereNull('rc.distance_to_event');
            }

            if (codes?.length) {
                query.whereIn('rt.status_housing', codes);
            }
        }

        if (Array.isArray(payment) && payment.length) {
            query.whereIn('rt.status_paid', payment);
        }
    }
}

module.exports = new TeamsService();
