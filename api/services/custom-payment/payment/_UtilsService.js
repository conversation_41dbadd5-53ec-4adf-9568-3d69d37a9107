
const { CUSTOM_PAYMENT } = require('../../../constants/payments');

class CustomPaymentUtils {
    getCustomerStripeFee(payment_method_info, amount) {
        const { payment_method_type, ach_percent, stripe_percent, stripe_fixed } = payment_method_info;

        switch (payment_method_type) {
            case StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD:
                return StripeService.customerStripeFee(amount, stripe_percent, stripe_fixed);
            case StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH:
                return StripeService.customerACHStripeFee(amount, ach_percent);
            default:
                throw new Error(`Invalid payment method ${payment_method_type}`)
        }
    }

    getMinPaymentAmount(payment_method_type) {
        switch (payment_method_type) {
            case StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD:
                return CUSTOM_PAYMENT.MIN_PAYMENT_AMOUNT.CARD;
            case StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH:
                return CUSTOM_PAYMENT.MIN_PAYMENT_AMOUNT.ACH;
            default:
                throw new Error(`Invalid payment method ${payment_method_type}`)
        }
    }

    getStripePercentUsed(payment_method_info) {
        const { payment_method_type, ach_percent, stripe_percent } = payment_method_info;

        switch (payment_method_type) {
            case StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD:
                return stripe_percent || StripeService.DEFAULT_STRIPE_PERCENT;
            case StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH:
                return ach_percent || StripeService.DEFAULT_ACH_PERCENT;
            default:
                throw new Error(`Invalid payment method ${payment_method_type}`)
        }
    }
}

module.exports = new CustomPaymentUtils();
