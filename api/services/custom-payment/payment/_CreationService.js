const { payment: paymentValidationSchema } = require('../../../validation-schemas/event-custom-payment');
const { CUSTOM_PAYMENT } = require('../../../constants/payments');
const PaymentRetryService = require("./_PaymentRetryService");


class CustomPaymentCreationService {
    constructor(balanceInfo, notificationsService, swUtils, utils) {
        this.balanceInfo = balanceInfo;
        this.notifications = notificationsService;
        this.swUtils = swUtils;
        this.utils = utils;
        this.paymentRetry = PaymentRetryService;
    }

    async process (eventID, userID, payment, sessionMode = CUSTOM_PAYMENT.PAYMENT_CREATION_MODE.DEFAULT) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        if(!userID) {
            throw { validation: 'User ID required' };
        }

        let { error } = paymentValidationSchema.validate(payment);

        if(!_.isEmpty(error)) {
            throw { validation: error.details[0].message };
        }

        let paymentIntent;

        try {
            let {
                customer,
                payment_method,
                payment_method_type,
                ach_percent,
                stripe_fixed,
                stripe_percent,
                offline_is_active
            } = await this._getPaymentMethodInfo(eventID, userID);

            if (!offline_is_active) {
                loggers.debug_log.verbose('Offline payment method is not active');
                return;
            }

            this._validatePaymentAmount(payment_method_type, payment.amount);

            let totals = this._getTotals(
                payment,
                {
                    payment_method_type,
                    ach_percent,
                    stripe_percent,
                    stripe_fixed
                }
            );

            try {
                //Create Payment Intent (with auto confirmation)
                paymentIntent = await this._proceedPaymentIntent(
                    totals.total,
                    customer,
                    payment,
                    payment_method,
                    payment_method_type,
                    userID,
                    eventID
                )
            } catch (err) {
                const {
                    paymentStatus,
                    notificationType,
                    paymentIntent
                } = this._getPaymentErrorTypeData(err);

                const customPayment = await this._processPaymentSaving(
                    eventID,
                    paymentIntent,
                    payment,
                    totals,
                    sessionMode,
                    payment_method,
                    paymentStatus
                );

                await this.notifications[notificationType].send({
                    payment_for_type: customPayment.payment_for_type,
                    custom_payment_id: customPayment.custom_payment_id,
                    event_id: customPayment.event_id,
                }).catch(err => loggers.errors_log.error(err));

                if(paymentStatus === CUSTOM_PAYMENT.PAYMENT_STATUS.REQUIRES_ACTION) {
                    return err.payment_intent.status;
                }

                throw err;
            }

            const customPayment = await this._processPaymentSaving(
                eventID,
                paymentIntent,
                payment,
                totals,
                sessionMode,
                payment_method,
                CUSTOM_PAYMENT.PAYMENT_STATUS.PENDING
            );

            if(payment.payment_for === CUSTOM_PAYMENT.PAYMENT_FOR.UNCOLLECTED_FEE) {
                await this.notifications.pending.send({
                    payment_for_type: customPayment.payment_for_type,
                    custom_payment_id: customPayment.custom_payment_id,
                    event_id: customPayment.event_id,
                }).catch(err => loggers.errors_log.error(err));
            }

            return paymentIntent.status;
        } catch (err) {
            if(!_.isEmpty(paymentIntent) && paymentIntent.charges && paymentIntent.charges.total_count > 0) {
                await StripeService.paymentCard.stripeService.refundDirectPlatformPayment(
                    paymentIntent.charges.data[0].id
                );
            }

            throw err;
        }
    }

    _getPaymentErrorTypeData (err) {
        const paymentIntent = err.payment_intent;

        const paymentRequiresAuthentication =
            StripeService.paymentCard.stripeService.paymentRequiresUserAuthentication(err);

        const authError = paymentIntent && paymentRequiresAuthentication;
        const denyError = paymentIntent && !paymentRequiresAuthentication;
        const genericError = !paymentIntent;

        if(authError) {
            return {
                paymentStatus: CUSTOM_PAYMENT.PAYMENT_STATUS.REQUIRES_ACTION,
                notificationType: 'authentication',
                paymentIntent
            }
        } else if(denyError) {
            return {
                paymentStatus: CUSTOM_PAYMENT.PAYMENT_STATUS.CANCELED,
                notificationType: 'error',
                paymentIntent
            }
        } else if(genericError) {
            return {
                paymentStatus: CUSTOM_PAYMENT.PAYMENT_STATUS.CANCELED,
                notificationType: 'error',
                paymentIntent: null
            }
        }
    }

    async _processPaymentSaving (
        eventID,
        paymentIntent,
        payment,
        totals,
        sessionMode,
        paymentMethodID,
        customPaymentStatus
    ) {
        let tr;

        try {
            tr = await Db.begin();

            let stripePaymentIntentID;

            if(paymentIntent) {
                stripePaymentIntentID = await this._savePaymentIntentRow(
                    tr, paymentIntent, totals, paymentIntent.last_payment_error
                );
            }

            //Save custom_payment row
            const customPayment = await this._saveStripeCustomPaymentRow(
                tr,
                customPaymentStatus,
                eventID,
                paymentMethodID,
                payment,
                totals,
                sessionMode,
                stripePaymentIntentID
            );

            await Promise.all([
                this._saveBalance(tr, customPayment, payment.amount),
                this.paymentRetry.process(tr, paymentMethodID, customPaymentStatus)
            ]);

            await tr.commit();

            return customPayment;
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async _saveStripeCustomPaymentRow (tr, status, eventID, paymentMethodID, payment, totals, sessionMode, paymentIntentID = null) {
        let dataToInsert = {
            event_id: eventID,
            status: status,
            payment_for: payment.payment_for,
            payment_for_type: payment.payment_for_type,
            amount: totals.total,
            net_profit: totals.amount,
            merchant_fee: totals.merchant_fee,
            stripe_payment_intent_id: paymentIntentID,
            stripe_payment_method_id: paymentMethodID,
            description: payment.description,
            session_mode: sessionMode
        };

        let query = knex('custom_payment').insert(dataToInsert).returning('custom_payment_id');

        let insertedData = await tr.query(query).then(result => result && result.rows[0]);

        if(!insertedData) {
            throw new Error('Custom Payment Row not created');
        }

        return {
            custom_payment_id: insertedData.custom_payment_id,
            net_profit: totals.amount,
            event_id: eventID,
            payment_for_type: payment.payment_for_type,
            payment_for: payment.payment_for,
        };
    }

    async _saveBalance(tr, customPayment, paymentAmount) {
        const balanceType = this.balanceInfo.getBalanceTypeForCustomPaymentType(customPayment.payment_for);
        const balanceInfoService = this.balanceInfo[balanceType];

        const balance = await balanceInfoService.getBalance(
            customPayment.event_id,
            customPayment.payment_for_type,
            paymentAmount
        );

        await balanceInfoService.save(tr, balance, customPayment.custom_payment_id);
    }

    async _savePaymentIntentRow (tr, paymentIntent, totals, lastPaymentError = null) {
        let [charge] = paymentIntent.charges.data;
        let paymentMethodType = charge?.payment_method_details?.type;

        //If charge wasn't created, we can take a payment method type from a payment intent payment methods list
        if(!paymentMethodType) {
            paymentMethodType = paymentIntent.payment_method_types[0];
        }

        let dataToInsert = {
            payment_intent_status: paymentIntent.status,
            stripe_charge_id: charge?.id,
            payment_intent_id: paymentIntent.id,
            amount: totals.total,
            stripe_fee: totals.merchant_fee,
            stripe_percent: this.swUtils.normalizeNumber(totals.percent * 100),
            stripe_card_fingerprint: charge?.payment_method_details[paymentMethodType]?.fingerprint
        }

        if(!_.isEmpty(lastPaymentError)) {
            dataToInsert.last_error_payment_method_id = lastPaymentError.payment_method.id;
            dataToInsert.last_error_client_secret = paymentIntent.client_secret;
        }

        let query = knex('stripe_payment_intent')
            .insert(dataToInsert)
            .returning('stripe_payment_intent_id');

        let stripeCustomPaymentID = await tr.query(query)
            .then(result => result && result.rows[0] && result.rows[0].stripe_payment_intent_id || null);

        if(!stripeCustomPaymentID) {
            throw new Error('Stripe Custom Payment Row not created');
        }

        return stripeCustomPaymentID;
    }

    _proceedPaymentIntent (amount, customer, payment, paymentMethod, paymentMethodType, userID, eventID) {
        let statementDescriptor = this._getStatementDescriptor(
            payment.payment_for,
            payment.payment_for_type
        );

        let metadata = this._generateMetadata(
            eventID,
            userID,
            payment.payment_for,
            payment.payment_for_type
        );

        return StripeService.paymentCard.stripeService.createPaymentIntentWithConfirm({
            amount: amount,
            metadata,
            customer,
            description: payment.description,
            payment_method: paymentMethod,
            payment_method_types: [paymentMethodType],
            statement_descriptor_suffix: statementDescriptor,
            expand: ['payment_method']
        });
    }

    _getStatementDescriptor (paymentFor, paymentForType) {
        if(paymentFor === CUSTOM_PAYMENT.PAYMENT_FOR.UNCOLLECTED_FEE) {
            return `${this._capitalizeFirstLetter(paymentForType)} SW Fee`;
        } else if(paymentFor === CUSTOM_PAYMENT.PAYMENT_FOR.LOST_DISPUTE_FEE_FAILED_ACH_FEE) {
            return `LostDispute ${this._capitalizeFirstLetter(paymentForType)}Fee`;
        } else {
            return '';
        }
    }

    _generateMetadata (eventID, userID, paymentFor, paymentForType) {
        return {
            event_id: eventID,
            //Set purchase_type = 'custom' to identify in stripe webhook
            purchase_type: 'custom',
            payment_for: paymentFor,
            payment_for_type: paymentForType,
            user_id: userID,
            project: 'sw'
        }
    }

    _capitalizeFirstLetter (string) {
        return string[0].toUpperCase() + string.slice(1);
    }

    _getTotals ({amount, total, merchant_fee}, payment_method_info) {
        amount = Number(amount);
        total = Number(total);
        merchant_fee = Number(merchant_fee);

        const customerStripeFee = this.utils.getCustomerStripeFee(payment_method_info, amount);

        if(merchant_fee !== customerStripeFee) {
            throw new Error('Merchant Fee Incorrect');
        }

        const recountTotal = this.swUtils.normalizeNumber(amount + customerStripeFee);

        if(total !== recountTotal) {
            throw new Error('Total Amount Incorrect');
        }

        const percentUsed = this.utils.getStripePercentUsed(payment_method_info);

        return { amount, total, merchant_fee, percent: percentUsed };
    }

    _validatePaymentAmount(payment_method_type, amount) {
        const minAmount = this.utils.getMinPaymentAmount(payment_method_type);

        if(minAmount > amount) {
            throw { validation: `Amount should be greater than ${minAmount}` };
        }
    }

    async _getPaymentMethodInfo (eventID, userID) {
        let query = knex('event_payment_method AS epm')
            .select(
                'usc.stripe_customer_id AS customer', 'epm.stripe_payment_method_id AS payment_method',
                'spm.type as payment_method_type', 'e.long_name AS event_name', 'u.email AS payer_email',
                'spm.offline_is_active as offline_is_active',
                knex.raw(`e.stripe_teams_fixed AS "stripe_fixed"`),
                knex.raw(`(
		            CASE
		                WHEN (e.stripe_teams_percent > 0)
		                    THEN (e.stripe_teams_percent / 100)
		                ELSE 0
		            END
		         )::NUMERIC "stripe_percent"`),
                knex.raw(`(
		            CASE 
		                WHEN (e.ach_teams_percent > 0)
		                    THEN (e.ach_teams_percent / 100)
		                ELSE 0
		            END
		         )::NUMERIC "ach_percent"`),
                knex.raw(`(
		            CASE
		                WHEN (spm.type = '${StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD}')
		                    THEN spm.card_last_4
                        WHEN (spm.type = '${StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH}')
		                    THEN spm.bank_account_last_4
		                ELSE ''
		            END
		         ) AS "card_last_4"`),
                knex.raw(`FORMAT('%s %s', INITCAP(u.first), INITCAP(u.last)) AS holder_name`)
            )
            .join('stripe_payment_method AS spm', 'spm.stripe_payment_method_id', 'epm.stripe_payment_method_id')
            .join('user_stripe_customer AS usc', 'usc.stripe_customer_id', 'spm.stripe_customer_id')
            .join('event AS e', 'e.event_id', 'epm.event_id')
            .join('user AS u', 'u.user_id', 'usc.user_id')
            .where('usc.user_id', userID)
            .where('epm.event_id', eventID);

        let {
            customer,
            payment_method,
            event_name,
            payer_email,
            payment_method_type,
            ach_percent,
            stripe_percent,
            stripe_fixed,
            card_last_4,
            offline_is_active,
        } = await Db.query(query).then(result => result && result.rows[0] || {});

        if(!payment_method || !customer) {
            throw new Error('Payment Method for user not found');
        }

        return {
            customer,
            payment_method,
            event_name,
            payer_email,
            payment_method_type,
            ach_percent: ach_percent ? Number(ach_percent) : StripeService.DEFAULT_ACH_PERCENT,
            stripe_percent: stripe_percent ? Number(stripe_percent) : StripeService.DEFAULT_STRIPE_PERCENT,
            stripe_fixed: stripe_fixed ? Number(stripe_fixed) : StripeService.DEFAULT_STRIPE_FIXED,
            card_last_4,
            offline_is_active
        };
    }
}

module.exports = CustomPaymentCreationService;
