
const swUtils = require('../../../lib/swUtils');

class PartialCustomPaymentRefund {
    constructor (payment, source) {
        if(_.isEmpty(payment) || !_.isObject(payment)) {
            throw new Error('Payment is empty');
        }

        if(!source) {
            throw new Error('Refund source is empty');
        }

        this.payment = payment;
        this.source = source;
    }

    async proceed (amountRefunded) {
        if(!amountRefunded) {
            throw new Error('Refunded amount required');
        }

        if(this.source === 'dashboard') {
            return this.__proceedStripeDashboardRefund(amountRefunded);
        } else {
            throw new Error('Refund source not supported');
        }
    }

    //TODO: move to separate file when one more source would be added
    __proceedStripeDashboardRefund (amountRefunded) {
        let totals = this.__recountTotals(amountRefunded);

        return this.__updateCustomPaymentRow(totals);
    }

    __recountTotals (amountRefunded) {
        let newAmount = swUtils.normalizeNumber((this.payment.amount + this.payment.amount_refunded) - amountRefunded);
        let newMerchantFee = this.__calculateMerchantFee(
            newAmount,
            this.payment.stripe_percent,
            this.payment.payment_method_type
        );
        let newNetProfit = swUtils.normalizeNumber(newAmount - newMerchantFee);

        return {
            amount: newAmount,
            merchantFee: newMerchantFee,
            netProfit: newNetProfit
        }
    }

    __calculateMerchantFee (amount, stripePercent, paymentMethodType) {
        switch (paymentMethodType) {
            case StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD:
                return StripeService.customerStripeFee(amount, stripePercent);
            case StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH:
                return StripeService.customerACHStripeFee(amount, stripePercent);
            default:
                throw new Error(`Invalid payment method ${paymentMethodType}`)
        }
    }

    __updateCustomPaymentRow ({ amount, merchantFee, netProfit }) {
        if(_.isNaN(amount)) {
            throw { validation: 'Amount should be a number' };
        }

        if(_.isNaN(merchantFee)) {
            throw { validation: 'Merchant Fee should be a number' };
        }

        if(_.isNaN(netProfit)) {
            throw { validation: 'Net Profit should be a number' };
        }

        let query = knex('custom_payment AS cp')
            .update({
                amount: amount,
                merchant_fee: merchantFee,
                net_profit: netProfit
            })
            .where('cp.custom_payment_id', this.payment.purchase_id);

        return Db.query(query).then(result => result && result.rowCount > 0)
            .then(updated => {
                if(!updated) {
                    throw new Error('Custom payment row not updated');
                }
            })
    }
}

module.exports = PartialCustomPaymentRefund;
