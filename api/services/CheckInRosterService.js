'use strict';

const
    co = require('co'),
    swUtils = require('../lib/swUtils'),
    knex = require('knex')({client:'pg'}),
    { USAV_SANC_BODY, ENTRY_STATUSES } = require('../constants/teams'),
    { US_COUNTRY_CODE } = require('../constants/common'),
    SEUtils = require('../lib/SEUtilsService'),
    SportEngineUtilsService = require("../lib/SEUtilsService");
/* (xxx) xxx-xxxx */
function tel (phone) {
  if (!phone) {
      return '';
  }

  if (_.isString(phone) && (phone.length >= 10)) {
    phone = phone.replace(/\D/g, '')
    phone = phone.replace(/^(\d\d\d)/g, '($1)')
    phone = phone.replace(/(\d{3})(\d*)$/, ' $1-$2')
  }

  return phone;
}

const MIN_AGE_FOR_SAFESPORT = 18;
const SAFESPORT_VALID_STATUS = '2';

module.exports = {
    EO_ROLE: 'EO',

    ROSTER_VALIDATION_SOURCE: {
        EO      : 'eo',
        SYSTEM  : 'system'
    },

    ROSTER_TEAM_REG_METHOD: {
        REGULAR: 'regular',
        PRIVATE_LINK: 'private_link'
    },

    /**
    * covered 😄👍
    *
    * We assume teams' members to be sorted by "role" property, so "staff" members are the last in
    * the members list
    **/
    validateAthletesJerseys: function (teams) {
        let notAthleteMember = member => (member.role !== 'athlete' || !member.jersey);
        let isStaffer = member => (member.role === 'staff');

        for (let currentTeam of teams) {

            let teamMembers = currentTeam.members;

            for (let i = 0; i < teamMembers.length; ++i) {
                let currentMember = currentTeam.members[i];

                if (isStaffer(currentMember)) {
                    break;
                }

                if (notAthleteMember(currentMember)) {
                    continue;
                }

                for (let j = (i + 1), dupMember; j < teamMembers.length; ++j) {
                    dupMember = currentTeam.members[j];

                    if (isStaffer(dupMember)) {
                      break;
                    }

                    if (notAthleteMember(dupMember)) {
                        continue;
                    }

                    if (dupMember.jersey === currentMember.jersey) {
                        dupMember.jersey_duplicate = true;
                        currentMember.jersey_duplicate = true;
                    }
                }
            }
        }

        return teams;
    },
    validateAthletesAAUJerseys: function (teams) {
        for (let currentTeam of teams) {
            const athletes = currentTeam.members.filter(({role, aau_jersey}) => role === 'athlete' && aau_jersey);

            athletes.forEach((athlete) => {
                if(!athlete.aau_jersey_duplicate) {
                    const dupMembers = athletes.filter(({aau_jersey}) => aau_jersey === athlete.aau_jersey);

                    if(dupMembers.length > 1) {
                        dupMembers.map(dupMembers => {
                            dupMembers.aau_jersey_duplicate = true
                            return dupMembers;
                        });
                    }
                }
            });
        }

        return teams;
    },
    renderPDF: function(event_id, master_club_id, team_id, cb) {
        return Promise.all([
            Promise.resolve().then(() => {
                let query = MEMBERS_SQL,
                    whereClause = '';

                if (master_club_id) {
                    whereClause += ' AND rc.master_club_id = ' + master_club_id;
                }
                if (team_id && !Array.isArray(team_id)) {
                    whereClause += ' AND rt.roster_team_id = ' + team_id;
                } else if (team_id && Array.isArray(team_id)) {
                    whereClause += ` AND rt.roster_team_id IN (${swUtils.numArrayToString(team_id)})`;
                }
                if (!team_id && !master_club_id) {
                    whereClause += ' AND rt.status_entry = 12';
                }

                whereClause += ` ORDER BY rc.club_name, d.name`;

                return Db.query(query.format(whereClause), [event_id, SAFESPORT_VALID_STATUS, MIN_AGE_FOR_SAFESPORT]);
            }),
            Db.query(
                `SELECT e.event_id, e.name, e.sport_sanctioning_id
                 FROM "event" e
                 WHERE e.event_id = $1`,
                 [event_id]
            )
        ]).then(results => {
            let teams = results[0].rows,
                event = results[1].rows[0] || {};
            event.isAauSanctioning = event.sport_sanctioning_id === USAV_SANC_BODY.AAU;
            if(event.isAauSanctioning) {
                this.validateAthletesAAUJerseys(teams);
            } else {
                this.validateAthletesJerseys(teams);
            }

            event.is_aau_sanctioning = event.sport_sanctioning_id === USAV_SANC_BODY.AAU
                || event.sport_sanctioning_id === USAV_SANC_BODY.JVA;

            if (cb) {
                cb(null, { teams, event, tel });
            } else {
                return { teams, event, tel };
            }
        }).catch(err => {
            if (cb) {
                cb(err);
            } else {
                throw err;
            }
        })
    },

    addDescriptionLinks: function (staffers, eventID) {
        if(!_.isArray(staffers) || !staffers.length) {
            return staffers;
        }

        return staffers.map(s => {
            if(s.primary_staff_barcodes_checkin && s.barcode) {
                s.checkin_description_link =
                    OnlineCheckinService.barcodes.getDescriptionLink({barcode: s.barcode, event_id: eventID});
            }

            return _.omit(s, ['barcode']);
        })
    },
    validateTeams: function(eventId, teamIds) {
        return Promise.all(teamIds.map((teamId) => this.validateTeam(eventId, teamId)));
    },
    validateTeam: function(eventId, rosterTeamId, role) {
        return Db.query(
            `SELECT 
                e.team_members_validation "rules",
                e.maxcount_staff_accept, e.has_rosters,
                e.sport_sanctioning_id,
                rc.country "team_club_country",
                (e.date_end < (NOW() AT TIME ZONE e.timezone)) "event_finished",
                e.season
            FROM "roster_team" rt
            INNER JOIN "roster_club" rc ON rt.roster_club_id = rc.roster_club_id AND rc.deleted IS NULL
            INNER JOIN "event" e ON rc.event_id = e.event_id AND e.deleted IS NULL
            WHERE e.event_id = $1
                AND rt.roster_team_id = ${rosterTeamId}`,
            [eventId]
        ).then(result => {
            const event = _.first(result.rows);
            if (_.isEmpty(event)) {
                throw {
                    validation: 'Event or Team Not Found'
                };
            }
            return event;
        }).then(event => {
            if (!event.rules || (event.rules && event.rules.skip_validation) || !event.has_rosters
                || (event.event_finished && role !== CheckInRosterService.EO_ROLE)
                || (event.season != sails.config.sw_season.current)) {

                let skipReasons = [];

                if (!event.rules) {
                    skipReasons.push('<Event has no validation rules>')
                }

                if (event.rules && event.rules.skip_validation) {
                    skipReasons.push('<Event has skip_validation rule>')
                }

                if (!event.has_rosters) {
                    skipReasons.push('<Event has no rosters>')
                }

                if (event.event_finished) {
                    skipReasons.push('<Event finished>');
                }

                if (event.season != sails.config.sw_season.current) {
                    skipReasons.push('<Event season != current season>')
                }

                loggers.debug_log.verbose(`Roster validation skipped: ${ skipReasons.join(' and ') }`);

                return Promise.resolve({});
            }

            let athletesMinAccept           = Number(event.rules.mincount_accept),
                athletesMaxAccept           = Number(event.rules.maxcount_accept),
                staffersMaxAccept           = Number(event.maxcount_staff_accept),
                primaryStaffersMaxAccept    = Number(event.rules.primary_maxcount_staff_accept),
                athletesMinEnter            = Number(event.rules.mincount_enter),
                athletesMaxEnter            = Number(event.rules.maxcount_enter),
                athletesMaxCheckin          = Number(event.rules.maxcount_checkin),
                athletesMinCheckin          = Number(event.rules.mincount_checkin),
                playersMinRefCerts          = event.rules.min_ref_certs
                                              ? Number(event.rules.min_ref_certs_value)
                                              : 0,
                playersMinScoreKeepCerts    = event.rules.min_score_keep_certs
                                              ? Number(event.rules.min_score_keep_certs_value)
                                              : 0,
                staffMinRefCerts            = event.rules.min_ref_certs_staff
                                              ? Number(event.rules.min_ref_certs_staff_value)
                                              : 0,
                staffMinScoreKeepCerts      = event.rules.min_score_keep_certs_staff
                                              ? Number(event.rules.min_score_keep_certs_staff_value)
                                              : 0,
                validatePrimaryStaffersData = event.rules.require_primary_staff_email_and_phone,
                oneTeamPerStafferRequired   = event.rules.one_team_per_staffer_required,
                eventHasUSAVSanctioning   = event.sport_sanctioning_id === USAV_SANC_BODY.USAV,
                eventHasNoOtherSanctioning= event.sport_sanctioning_id !== USAV_SANC_BODY.OTHER,
                staffRolesAllowed           = event.rules.staff_roles_allowed || {},
                clubHasNotUsCountry       = event.team_club_country !== US_COUNTRY_CODE,
                validationRules             = _.defaults(event.rules, {
                    athletesMinAccept           : athletesMinAccept || null,
                    athletesMaxAccept           : athletesMaxAccept || null,
                    staffersMaxAccept           : staffersMaxAccept || null,
                    primaryStaffersMaxAccept    : primaryStaffersMaxAccept || null,
                    athletesMinEnter            : athletesMinEnter  || null,
                    athletesMaxEnter            : athletesMaxEnter  || null,
                    athletesMaxCheckin          : athletesMaxCheckin|| null,
                    athletesMinCheckin          : athletesMinCheckin|| null,
                    validatePrimaryStaffersData,
                    playersMinRefCerts,
                    playersMinScoreKeepCerts,
                    staffMinRefCerts,
                    staffMinScoreKeepCerts,
                    eventHasUSAVSanctioning,
                    oneTeamPerStafferRequired,
                    staffRolesAllowed,
                    eventHasNoOtherSanctioning,
                    clubHasNotUsCountry,
                }
            );

            return filterValidatedByEOTeams(rosterTeamId, eventId).then(filteredTeam => {

                if(!filteredTeam) {
                    return Promise.resolve({});
                }

                return __validateTeamMembers(eventId, event.sport_sanctioning_id, filteredTeam, validationRules);
            })
        });
    },
    validateTeamJerseys: function (eventId, rosterTeamId) {
        return __isUniqueUniform(eventId, rosterTeamId);
    },
    validateTeamAAUJerseys: function (eventId, rosterTeamId) {
        return __isUniqueAAUUniform(eventId, rosterTeamId);
    },
    validateMasterTeamRosterWithRosterTeamId: function (eventId, rosterTeamId) {
        return Db.query(
            `SELECT rt.master_team_id FROM "roster_team" rt WHERE rt.roster_team_id = $1`,
            [rosterTeamId]
        ).then(result => {
            return __masterTeamRosterValidator(eventId, _.first(result.rows).master_team_id);
        })
    },
    validateMasterTeamRosterWithMasterTeamId: function (eventId, masterTeamId) {
        return __masterTeamRosterValidator(eventId, masterTeamId);
    },
    validateCheckIn: function (eventId, teams) {
        return __validateCheckInRoster(eventId, teams);

    },
    validateSanction: function(masterClubID, eventID) {
        let sql = knex.withRecursive('event_sanct', function() {
            this.select('sport_sanctioning_id').from('event')
                .where({
                    event_id: eventID,
                    deleted: null
                });
        }).select(1).from('event_sanct as es')
            .where('es.sport_sanctioning_id', USAV_SANC_BODY.OTHER)
            .orWhereIn('sport_sanctioning_id', function() {
                this.select('mcs.sport_sanctioning_id')
                    .from('master_club_sanctioning AS mcs')
                    .where('mcs.master_club_id', masterClubID)
            });

        return Db.query(sql.toString())
            .then(({ rowCount }) => rowCount > 0);
    },
    validateSeasonality: async ({ masterTeamID, divisionID, eventID, rosterTeamID }) => {
       const query = knex('division as d')
           .select({ division_seasonality: 'd.seasonality' })
           .join('event as e', (table) => {
               table.on('e.event_id', 'd.event_id')
                   .andOn(knex.raw(`e.sport_sanctioning_id = ?`, USAV_SANC_BODY.USAV))
           })
           .where('d.division_id', divisionID)
           .where('d.event_id', eventID);

       if(masterTeamID) {
           query.join('master_team as mt', knex.raw('mt.master_team_id = ?', masterTeamID))
               .select({ team_seasonality: 'mt.seasonality' });
       } else if(rosterTeamID) {
           query.join('roster_team as rt', (table) => {
               table.on('rt.roster_team_id', rosterTeamID)
                   .andOn('rt.event_id', 'd.event_id')
           })
               .select({ team_seasonality: 'rt.seasonality' })
       } else {
           throw new Error('Master or roster team ID required');
       }

       const {
           division_seasonality,
           team_seasonality
       } = await Db.query(query).then(result => result?.rows?.[0] || {});

       if(team_seasonality !== division_seasonality) {
           throw { validation: `Team's seasonality should be equal division's` };
       }
    },
    validateAge: async function (data) {

        let masterTeamId = data.masterTeamId;
        let rosterTeamId = data.rosterTeamId;
        let divisionId = data.divisionId;

        let sql = '', values = [];

        if(masterTeamId) {

            sql = `
            SELECT (
                    CASE WHEN mt.age <= d.max_age THEN TRUE
                        ELSE FALSE
                    END
                ) AS allow_access
            FROM master_team mt
            LEFT JOIN division d
                ON d.division_id = $2
            WHERE mt.master_team_id = $1
            AND d.division_id = $2
            `;
            values = [masterTeamId, divisionId];

        } else {

            sql = `
            SELECT (
                    CASE WHEN rt.age <= d.max_age THEN TRUE
                        ELSE FALSE
                    END
                ) AS allow_access
            FROM roster_team rt
            LEFT JOIN division d
                ON d.division_id = $2
            LEFT JOIN "event" e
                ON e.event_id = rt.event_id
            WHERE rt.roster_team_id = $1
            AND d.division_id = $2
            `;
            values = [rosterTeamId, divisionId];

        }

        let result = await Db.query(sql, values);

        return result.rows.length ? result.rows[0].allow_access : false;

    }

};

function __validateCheckInRoster(eventId, teams) {
    return Db.query(
        `SELECT
            e.team_members_validation "rules",
            (e.date_end < (NOW() AT TIME ZONE e.timezone)) "event_finished"
        FROM "event" e
        WHERE e.event_id = $1
        AND e.deleted IS NULL`,
        [eventId]
    ).then(result => {
        let event = _.first(result.rows);
        if (_.isEmpty(event)) {
            throw {
                validation: 'Event Not Found'
            };
        }
        return event;
    }).then(event => {
        if ( !event.rules || (event.rules && event.rules.skip_validation) || event.event_finished ) {
            return [];
        };

        return __validateCheckInRosterQty(event, teams);
    })
};

function __validateCheckInRosterQty(event, teams) {
     let teamsString = swUtils.numArrayToString(teams, 'roster_team_id', true);

     return Db.query(
            `SELECT 
                COUNT(ra.roster_team_id)
            FROM "roster_athlete" ra
            WHERE ra.roster_team_id IN (${teamsString})
                AND ra.deleted IS NULL
            GROUP BY ra.roster_team_id`
    ).then(result => {

        if (_.isEmpty(result.rows)) {
            return teams.map(() => {
                return {errors:{roster:[`Lack of athletes in this team. Min is ${ Number(event.rules.mincount_checkin) }`]}};
            });
        };

        return result.rows.map(t => {
            let athletesMinCheckin = Number(event.rules.mincount_checkin),
                athletesMaxCheckin = Number(event.rules.maxcount_checkin),
                athleteCount = Number(t.count),
                rosterErrors = [];

            if (athletesMinCheckin && athletesMinCheckin > athleteCount) {
                rosterErrors.push(`Lack of athletes in this team. Min is ${athletesMinCheckin}`);
            }
            if (athletesMaxCheckin && athletesMaxCheckin < athleteCount) {
                rosterErrors.push(`Too many athletes in this team. Max is ${athletesMaxCheckin}`);
            }

            return { errors: { roster: rosterErrors}};
        });
    })
}

function __masterTeamRosterValidator (eventId, masterTeamId) {

    return Db.query(
        `SELECT
            e.sport_sanctioning_id,
            COALESCE(rc.country, mc.country) AS team_club_country,
            e.team_members_validation "rules",
            (e.date_end < (NOW() AT TIME ZONE e.timezone)) "event_finished"
        FROM "master_team" mt
        LEFT JOIN "master_club" mc ON mc.master_club_id = mt.master_club_id
        LEFT JOIN "roster_club" rc ON mc.master_club_id = rc.master_club_id
            AND rc.event_id = $1
        LEFT JOIN "event" e ON rc.event_id = e.event_id AND e.deleted IS NULL
        WHERE mt.master_team_id = $2
            AND mt.deleted IS NULL`,
        [eventId, masterTeamId]
    ).then(result => {
        const event = _.first(result.rows);
        if (_.isEmpty(event)) {
            throw {
                validation: 'Event Not Found'
            };
        }
        return event;
    }).then(event => {
        return __valideteRosterAthletesCount(event, masterTeamId);
    })

}

function __valideteRosterAthletesCount(event, masterTeamId) {
    let query = `SELECT COUNT(ma.*)
        FROM "master_athlete" ma
        INNER JOIN "master_team" mt
            ON mt.master_team_id = ma.master_team_id
            AND mt.deleted IS NULL
            AND mt.master_club_id = ma.master_club_id
        WHERE ma.deleted IS NULL
            AND ma.season = $2
            AND ma.master_team_id = $1`;

    if(event.sport_sanctioning_id === USAV_SANC_BODY.USAV && event.team_club_country === US_COUNTRY_CODE) {
        query += ` AND ma.usav_number IS NOT NULL`;
    } else if(event.sport_sanctioning_id === USAV_SANC_BODY.JVA || event.sport_sanctioning_id === USAV_SANC_BODY.AAU) {
        query += ` AND (ma.aau_membership_id IS NOT NULL OR ma.usav_number IS NULL)`;
    } else if(event.sport_sanctioning_id !== USAV_SANC_BODY.OTHER && event.sport_sanctioning_id !== USAV_SANC_BODY.USAV ) {
        query += ` AND (ma.usav_number IS NULL AND ma.aau_membership_id IS NULL)`;
    }

    return Db.query(query, [masterTeamId, sails.config.sw_season.current])
        .then(result => {
            if (!event.rules || (event.rules && event.rules.skip_validation) || event.event_finished) {
                return [];
            }

            let athletesMinEnter = Number(event.rules.mincount_enter),
                athletesMaxEnter = Number(event.rules.maxcount_enter),
                athleteCount     = Number(_.first(result.rows).count),
                errors           = [];

            if (athletesMinEnter && athletesMinEnter > athleteCount) {
                errors.push(`Lack of athletes in this team. Min is ${athletesMinEnter}`);
            }
            if (athletesMaxEnter && athletesMaxEnter < athleteCount) {
                errors.push(`Too many athletes in this team. Max is ${athletesMaxEnter}`);
            }

            return errors;
        })
}

function __validateTeamMembers (eventId, sportSanctioningId, rosterTeamId, validationRules) {
    return co(function* () {
        let validationResults = yield ({
            jersey            : sportSanctioningId !== USAV_SANC_BODY.AAU
                                ? __isUniqueUniform(eventId, rosterTeamId)
                                : Promise.resolve(),
            aau_jersey        : sportSanctioningId === USAV_SANC_BODY.AAU
                                ? __isUniqueAAUUniform(eventId, rosterTeamId)
                                : Promise.resolve(),
            roster            : __rosterQtyValidator(
                                      eventId, rosterTeamId, validationRules),
            birthdate         : __birthDateValidator(eventId, rosterTeamId),
            assignedRole      : __assignedRoleValidator(eventId, rosterTeamId),

            hc_cell_phone     : __hasHeadCoachCellPhone(eventId, rosterTeamId),
            invalid_primary   : validationRules.oneTeamPerStafferRequired
                                  ?__primaryStaffCheck(eventId, rosterTeamId)
                                  :Promise.resolve(),
            impact            : validationRules.hc_impact
                                  ?__impactCoachesValidator(eventId, rosterTeamId)
                                  :Promise.resolve(),
            bg                : validationRules.bg_screening
                                  ?__bgScreeningValidator(eventId, rosterTeamId)
                                  :Promise.resolve(),
            safesport         : validationRules.ss_cert
                                  ?__safeSportValidator(eventId, rosterTeamId)
                                  :Promise.resolve(),
            hc_unique         : validationRules.hc_unique
                                  ?__checkHCUniquePerEvent(eventId, rosterTeamId)
                                  :Promise.resolve(),
            empty_uniform     : validationRules.empty_uniform
                                  ?__emptyUniform(eventId, rosterTeamId, sportSanctioningId)
                                  :Promise.resolve(),
            empty_pos         : validationRules.athlete_pos
                                    ?__emptyPosition(eventId, rosterTeamId)
                                    :Promise.resolve(),
            staffer_phone     : validationRules.staff_cell_phone
                                    ?__staffCellPhone(eventId, rosterTeamId)
                                    :Promise.resolve(),
            players_ref_certs_count   : validationRules.playersMinRefCerts > 0
                                    ? __refCertsCount(eventId, rosterTeamId, validationRules.playersMinRefCerts, 'athlete')
                                    : Promise.resolve(),
            players_score_keep_certs_count : validationRules.playersMinScoreKeepCerts > 0
                                    ? __scoreKeepCertsCount(eventId, rosterTeamId, validationRules.playersMinScoreKeepCerts, 'athlete')
                                    : Promise.resolve(),
            staff_ref_certs_count   : validationRules.staffMinRefCerts > 0
                                    ? __refCertsCount(eventId, rosterTeamId, validationRules.staffMinRefCerts, 'staff')
                                    : Promise.resolve(),
            staff_score_keep_certs_count : validationRules.staffMinScoreKeepCerts > 0
                                    ? __scoreKeepCertsCount(eventId, rosterTeamId, validationRules.staffMinScoreKeepCerts, 'staff')
                                    : Promise.resolve(),
            player_usav_membership_status: validationRules.eventHasUSAVSanctioning
                                    ? __validateTeamPlayersUSAVMembershipStatus(eventId, rosterTeamId)
                                    : Promise.resolve(),
            staff_usav_membership_status: validationRules.eventHasUSAVSanctioning
                                    ? __validateTeamStaffersUSAVMembershipStatus(eventId, rosterTeamId)
                                    : Promise.resolve(),
            unic_name         : __isUnicName(eventId,rosterTeamId),
            hc_not_one        : __teamHasNotOneHeadCoach(eventId,rosterTeamId),
            not_valid_primary_staffers: validationRules.validatePrimaryStaffersData
                                    ? __primaryStaffersWithoutPhoneOrEmail(eventId, rosterTeamId)
                                    : Promise.resolve(),
            adult_athletes_ss : validationRules.validate_athlete_ss
                                    ? __checkAthletesSafeSport(eventId, rosterTeamId)
                                    : Promise.resolve(),
            usav_seasonality  : validationRules.eventHasUSAVSanctioning
                                    ? __checkAthletesSeasonality(eventId, rosterTeamId)
                                    : Promise.resolve(),
            athlete_sanctioning : validationRules.eventHasUSAVSanctioning && validationRules.clubHasNotUsCountry
                ? Promise.resolve() : validationRules.eventHasNoOtherSanctioning
                    ? __checkAthletesSanctioning(eventId, sportSanctioningId, rosterTeamId)
                    : Promise.resolve(),
            staff_sanctioning  : validationRules.eventHasUSAVSanctioning && validationRules.clubHasNotUsCountry
                ? Promise.resolve() : validationRules.eventHasNoOtherSanctioning
                    ? __checkStaffersSanctioning(eventId, sportSanctioningId,  rosterTeamId)
                    : Promise.resolve(),
            staff_roles_allowed: __checkStaffRolesAllowed(eventId, rosterTeamId, validationRules.staffRolesAllowed),
        });

        let isValid = true;

        for(let i = 0, keys = Object.keys(validationResults), l = keys.length; i < l; ++i) {
            if(validationResults[keys[i]]) {
                isValid = false;
                break;
            }
        }

        yield (__updateTeamValidationResult(eventId, rosterTeamId, isValid));
        return validationResults;
    });
}


/**
 * Check if roster meats minimum Referee Certification requirement
 *
 * @param {integer} eventId
 * @param {integer} rosterTeamId
 * @param {integer} minScoreKeepCerts
 * @param {string}  role
                    can be 'athlete' or 'staff'
 * @returns
           undefined, if criterion is met
           otherwise {actual, shouldBe}
 */
function __refCertsCount(eventId, rosterTeamId, minRefCerts, role) {
    return Db.query(
            `
        SELECT COUNT (*) as ref_certs_count
        FROM v_roster_member
        WHERE COALESCE(NULLIF(ref_end_date, '')::DATE, '19700101') > date_end
              AND role = $3
              AND event_id = $1 and roster_team_id = $2
        `, [eventId, rosterTeamId, role])
        .then((result) => {
            if (result.rows[0].ref_certs_count >= minRefCerts) return;
            return {
                actual   : result.rows[0].ref_certs_count,
                shouldBe : minRefCerts
            };
        });
}

function __checkAthletesSeasonality (eventID, rosterTeamID) {
    const query = knex('roster_team AS rt')
        .select(
            knex.raw(`
                (
                    SELECT ROW_TO_JSON(data)
                    FROM (
                        SELECT 
                            FORMAT('%s %s', INITCAP(ma.first), INITCAP(ma.last)) AS name,
                            INITCAP(COALESCE(ma.seasonality, 'No seasonality')) AS seasonality
                        ) data 
                ) AS athlete
            `),
            knex.raw(`INITCAP(COALESCE(rt.seasonality, mt.seasonality)) AS team_seasonality`)
            )
        .join('roster_athlete AS ra', (table) => {
            table.on('ra.roster_team_id', 'ra.roster_team_id')
                .andOnNull('ra.deleted_by_user')
                .andOnNull('ra.deleted')
                .andOn(knex.raw(`(ra."as_staff" = 0 OR ra."as_staff" IS NULL)`))
        })
        .join('master_team AS mt', 'mt.master_team_id', 'rt.master_team_id')
        .join('master_athlete AS ma', (table) => {
            table.on('mt.master_team_id', 'ma.master_team_id')
                .andOn(knex.raw(`(
                    (
                        ma.seasonality IS NULL OR (ma.seasonality <> '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.REGIONAL}'
                            AND ma.seasonality <> COALESCE(rt.seasonality, mt.seasonality))
                    ) OR (
                        ma.seasonality IS NULL OR (ma.seasonality = '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.REGIONAL}'
                            AND COALESCE(rt.seasonality, mt.seasonality) <> '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.FULL}')
                    )
                )`))
                .andOn('ma.master_athlete_id', 'ra.master_athlete_id')
        })
        .where('rt.event_id', eventID)
        .where('rt.roster_team_id', rosterTeamID);

    return Db.query(query).then(result => {
        if(result.rowCount > 0) {
            return result.rows;
        }
    })
}

function __checkAthletesSanctioning (eventId, sportSanctioningId, rosterTeamId) {
    let query =
        `SELECT FORMAT('%s %s', INITCAP(ma.first), INITCAP(ma.last)) AS name,
            ma.usav_number AS usav_number,
            ma.aau_membership_id AS aau_membership_id
        FROM roster_team rt
             JOIN master_team mt ON mt.master_team_id = rt.master_team_id AND mt.deleted IS NULL
             JOIN roster_athlete ra
                  ON ra.roster_team_id = rt.roster_team_id AND ra.deleted IS NULL AND ra.deleted_by_user IS NULL AND
                     ra.event_id = rt.event_id
             JOIN master_athlete ma
                  ON ma.master_athlete_id = ra.master_athlete_id AND ma.master_team_id = mt.master_team_id
        WHERE rt.event_id = $1
            AND rt.roster_team_id = $2
            AND rt.deleted IS NULL`;

    if(sportSanctioningId === USAV_SANC_BODY.USAV) {
        query += ` AND ma.usav_number IS NULL`;
    } else if(sportSanctioningId === USAV_SANC_BODY.JVA || sportSanctioningId === USAV_SANC_BODY.AAU) {
        query += ` AND (ma.aau_membership_id IS NULL AND ma.usav_number IS NOT NULL)`;
    } else {
        query += ` AND (ma.usav_number IS NOT NULL OR ma.aau_membership_id IS NOT NULL)`;
    }

    const sportSanctioning = _.invert(USAV_SANC_BODY)[sportSanctioningId];

    return Db.query(query, [eventId, rosterTeamId])
        .then(result => {
            if(result.rowCount > 0) {
                return {athletes: result.rows, sportSanctioning};
            }
        })
}

function __checkStaffersSanctioning (eventId, sportSanctioningId, rosterTeamId) {
    let query =
        `SELECT FORMAT('%s %s', INITCAP(ms.first), INITCAP(ms.last)) AS name,
            ms.usav_number AS usav_number,
            ms.aau_membership_id AS aau_membership_id
        FROM roster_team rt
            JOIN master_team mt ON mt.master_team_id = rt.master_team_id AND mt.deleted IS NULL
            JOIN roster_staff_role rsr
                ON rsr.roster_team_id = rt.roster_team_id AND rsr.deleted IS NULL AND rsr.deleted_by_user IS NULL
            JOIN master_staff_role msr
                ON msr.master_staff_id = rsr.master_staff_id AND msr.master_team_id = rsr.master_team_id
            JOIN master_staff ms ON ms.master_staff_id = msr.master_staff_id
        WHERE rt.event_id = $1
            AND rt.roster_team_id = $2
            AND rt.deleted IS NULL`;

    if(sportSanctioningId === USAV_SANC_BODY.USAV) {
        query += ` AND ms.usav_number IS NULL`;
    } else if(sportSanctioningId === USAV_SANC_BODY.JVA || sportSanctioningId === USAV_SANC_BODY.AAU) {
        query += ` AND (ms.aau_membership_id IS NULL AND ms.usav_number IS NOT NULL)`;
    } else {
        query += ` AND (ms.usav_number IS NOT NULL OR ms.aau_membership_id IS NOT NULL)`;
    }

    const sportSanctioning = _.invert(USAV_SANC_BODY)[sportSanctioningId];

    return Db.query(query, [eventId, rosterTeamId])
        .then(result => {
            if(result.rowCount > 0) {
                return {staffers: result.rows, sportSanctioning};
            }
        })
}

// Athletes over 18 years of age must have SafeSport
function __checkAthletesSafeSport(eventID, rosterTeamID) {
    let query = knex('roster_team AS rt')
        .select(knex.raw(`FORMAT('%s %s', INITCAP(ma.first), INITCAP(ma.last)) AS name`))
        .join('event AS e', 'e.event_id', 'rt.event_id')
        .join('roster_athlete AS ra', (table) => {
            table.on('ra.roster_team_id', 'rt.roster_team_id')
            table.onNull('ra.deleted_by_user')
            table.onNull('ra.deleted')
        })
        .join('master_athlete AS ma', (table) => {
            table
                .on('ma.master_team_id', 'rt.master_team_id')
                .on('ma.master_athlete_id', 'ra.master_athlete_id')
        })
        .where((builder) => {
            builder.where(knex.raw(`
                CASE
                    WHEN date_part('year', age(e.date_end::DATE, ma.birthdate))::INTEGER >= ?
                        THEN (ma.safesport_statusid <> ? OR ma.safesport_statusid IS NULL)
                    ELSE FALSE
                END
            `, [MIN_AGE_FOR_SAFESPORT, SAFESPORT_VALID_STATUS]))
        })
        .where('rt.event_id', eventID)
        .where('rt.roster_team_id', rosterTeamID);

    return Db.query(query).then(result => {
        if(result.rowCount > 0) {
            return result.rows;
        }
    })
}

/**
 * Check if staff roles allowed for this event
 * @param eventID
 * @param rosterTeamID
 * @param staffRolesAllowed
 * @private
 */
async function __checkStaffRolesAllowed(eventID, rosterTeamID, staffRolesAllowedMap) {
    if (!staffRolesAllowedMap) {
        return;
    }
    const notAllowedRoles = Object.entries(staffRolesAllowedMap).reduce((acc, role) => {
        if (!role[1]) {
            acc.push(role[0]);
        }
        return acc;
    }, []);

    if (!notAllowedRoles.length) {
        return;
    }

    const query = `
        SELECT rr.role_id AS role_id,
               rr.name AS role_name,
               rt.roster_team_id AS roster_team_id,
               ms.first AS first,
               ms.last AS last
        FROM roster_team AS rt
            LEFT JOIN roster_staff_role AS rsr ON rsr.roster_team_id = rt.roster_team_id
                AND rsr.deleted_by_user IS NULL AND rsr.deleted IS NULL
            INNER JOIN master_staff AS ms ON ms.master_staff_id = rsr.master_staff_id
            LEFT JOIN master_staff_role AS msr ON msr.master_staff_id = rsr.master_staff_id
                AND msr.master_team_id = rsr.master_team_id
            JOIN role AS rr ON rr.role_id = COALESCE(NULLIF(rsr.role_id, 0), msr.role_id)
                AND rr.role_id IN (${notAllowedRoles.join(",")})
        WHERE rt.event_id = $1 AND rt.roster_team_id = $2
    `;
    const staff = await Db.query(query, [eventID, rosterTeamID]);

    if (staff.rowCount > 0) {
        return staff.rows.reduce((acc, staff) => {
            const message = `Staff ${staff.first} ${staff.last} has role ${staff.role_name} which is not allowed for this event`;
            acc.push(message);

            return acc;
        }, []);
    }
}

/**
 * Check if roster meats minimum Score Keeping Certification requirement
 *
 * @param {integer} eventId
 * @param {integer} rosterTeamId
 * @param {integer} minScoreKeepCerts
 * @param {string}  role
                    can be 'athlete' or 'staff'
 * @returns
           undefined, if criterion is met
           otherwise {actual, shouldBe}
 */
function __scoreKeepCertsCount(eventId, rosterTeamId, minScoreKeepCerts, role) {
    return Db.query(
            `
        SELECT COUNT (*) as score_keep_certs_count
        FROM v_roster_member
        WHERE COALESCE(NULLIF(score_end_date, '')::DATE, '19700101') > date_end
              AND role = $3
              AND event_id = $1 and roster_team_id = $2
        `, [eventId, rosterTeamId, role])
        .then((result) => {
            if (result.rows[0].score_keep_certs_count >= minScoreKeepCerts) return;
            return {
                actual: result.rows[0].score_keep_certs_count,
                shouldBe: minScoreKeepCerts
            };
        });
};


function __updateTeamValidationResult (eventId, rosterTeamId, isValid) {
      return Db.query(
        `UPDATE "roster_team" rt 
         SET "is_valid_roster" = $3, 
             "roster_validated_at" = NOW(),
             "roster_validated_by" = $4 
         WHERE "roster_team_id" = $2 AND "event_id" = $1`,
        [eventId, rosterTeamId, isValid, CheckInRosterService.ROSTER_VALIDATION_SOURCE.SYSTEM]
      );
}

// Unique Junior's Uniform per team
function __isUniqueUniform (eventId, rosterTeamId) {
  return Db.query(
      `SELECT COALESCE(ra.jersey, ma.jersey) "jersey", COUNT(ra.*), 
          ARRAY_TO_JSON(
              ARRAY_AGG(
                  (
                      SELECT ROW_TO_JSON(a) 
                      FROM ( 
                          SELECT  FORMAT('%s %s', INITCAP(ma.first), INITCAP(ma.last)) "name",   
                                  ma.organization_code "usav"
                      ) "a"
                  )
              )
          )"athletes"
      FROM "roster_athlete" ra 
      INNER JOIN "master_athlete" ma 
          ON ma.master_athlete_id = ra.master_athlete_id
          AND ma.season = $3
      WHERE ra.roster_team_id = $2
          AND ra.event_id = $1
          AND ra.deleted_by_user IS NULL
          AND ra.deleted IS NULL
          AND (ra."as_staff" = 0 OR ra."as_staff" IS NULL)
          AND COALESCE(ra.jersey, ma.jersey) IS NOT NULL
      GROUP BY COALESCE(ra.jersey, ma.jersey)
      HAVING COUNT(ra.*) > 1`,
     [eventId, rosterTeamId, sails.config.sw_season.current]
  ).then(function (result) {
      if(result.rowCount === 0)
        return;
      return result.rows;
  });
}

// Unique AAU Junior's Uniform per team
function __isUniqueAAUUniform (eventId, rosterTeamId) {
  return Db.query(
      `SELECT COALESCE(ra.aau_jersey, ma.aau_jersey) "aau_jersey", COUNT(ra.*),
          ARRAY_TO_JSON(
              ARRAY_AGG(
                  (
                      SELECT ROW_TO_JSON(a)
                      FROM (
                          SELECT FORMAT('%s %s', INITCAP(ma.first), INITCAP(ma.last)) "name",
                                  ma.organization_code "usav"
                      ) "a"
                  )
              )
          )"athletes"
      FROM "roster_athlete" ra
      INNER JOIN "master_athlete" ma
          ON ma.master_athlete_id = ra.master_athlete_id
          AND ma.season = $3
      WHERE ra.roster_team_id = $2
          AND ra.event_id = $1
          AND ra.deleted_by_user IS NULL
          AND ra.deleted IS NULL
          AND (ra."as_staff" = 0 OR ra."as_staff" IS NULL)
          AND COALESCE(ra.aau_jersey, ma.aau_jersey) IS NOT NULL
      GROUP BY COALESCE(ra.aau_jersey, ma.aau_jersey)
      HAVING COUNT(ra.*) > 1`,
     [eventId, rosterTeamId, sails.config.sw_season.current]
  ).then(function (result) {
      if(result.rowCount === 0)
        return;
      return result.rows;
  });
}

function __emptyUniform (eventId, rosterTeamId, sportSanctioningId) {
    let jerseyWhereSql = 'COALESCE(ra.jersey, ma.jersey) IS NULL';

    if(sportSanctioningId === USAV_SANC_BODY.AAU) {
        jerseyWhereSql = 'COALESCE(ra.aau_jersey, ma.aau_jersey) IS NULL';
    }

    return Db.query(
        `SELECT
             FORMAT(
                 '%s %s%s',
                 INITCAP(ma.first),
                 INITCAP(ma.last),
                 CASE
                     WHEN ma.organization_code IS NOT NULL THEN FORMAT(' (%s)', ma.organization_code)
                     ELSE ''
                 END
             ) AS "name"
         FROM "roster_athlete" ra 
         INNER JOIN "master_athlete" ma 
             ON ma.master_athlete_id = ra.master_athlete_id
             AND ma.season = $3
         WHERE ra.roster_team_id = $2
             AND ra.event_id = $1
             AND ra.deleted_by_user IS NULL
             AND ra.deleted IS NULL
             AND (ra."as_staff" = 0 OR ra."as_staff" IS NULL)
             AND ${jerseyWhereSql}`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then(function (result) {
        if(result.rowCount === 0)
          return;
        return result.rows;
    });
}

function __emptyPosition (eventId, rosterTeamId) {
    return Db.query(
        `SELECT
             FORMAT(
                 '%s %s%s',
                 INITCAP(ma.first),
                 INITCAP(ma.last),
                 CASE
                     WHEN ma.organization_code IS NOT NULL THEN FORMAT(' (%s)', ma.organization_code)
                     ELSE ''
                 END
             ) AS "name"
         FROM "roster_athlete" ra 
         INNER JOIN "master_athlete" ma 
             ON ma.master_athlete_id = ra.master_athlete_id
             AND ma.season = $3
         WHERE ra.roster_team_id = $2
             AND ra.event_id = $1
             AND ra.deleted_by_user IS NULL
             AND ra.deleted IS NULL
             AND (ra."as_staff" = 0 OR ra."as_staff" IS NULL)
             AND COALESCE(ra.sport_position_id, ma.sport_position_id) IS NULL`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then(function (result) {
        if(result.rowCount === 0)
          return;
        return result.rows;
    });
}

function __impactCoachesValidator (eventId, rosterTeamId) {
    return Db.query(
        `SELECT
             FORMAT('%s %s', ms.first, ms.last) "name",
             COALESCE(rr.name, mr.name) "role_name",
             (
                CASE 
                  WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                  WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                  ELSE NULL
                END  
             ) "cert"
         FROM "roster_staff_role" rsr 
         INNER JOIN "roster_team" rt 
             ON rt.roster_team_id = rsr.roster_team_id 
             AND rt.event_id = $1
         INNER JOIN "master_staff" ms 
             ON ms.master_staff_id = rsr.master_staff_id
         LEFT JOIN "master_staff_role" msr 
             ON msr.master_staff_id = rsr.master_staff_id
             AND msr.master_team_id = rsr.master_team_id
         LEFT JOIN "role" rr 
             ON rr.role_id = rsr.role_id
         LEFT JOIN "role" mr 
             ON mr.role_id = msr.role_id
         WHERE rsr.roster_team_id = $2
             AND rsr.deleted IS NULL
             AND rsr.deleted_by_user IS NULL
             AND COALESCE(NULLIF(rsr.role_id, 0), msr.role_id) IN (4, 5)
             AND ms."is_impact" IS NOT TRUE
             AND ms.season = $3`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then(function (result) {
      if(result.rowCount === 0)
        return;
      return result.rows;
    });
}

function __isUnicName(eventId, rosterTeamId) {
    let season = sails.config.sw_season.current,
        errors = [];

    return Db.query(`
        SELECT rt.team_name, rt.gender
        FROM roster_team rt
        WHERE rt.event_id = $1 AND rt.roster_team_id = $2
        `, [eventId, rosterTeamId]
    ).then(result => {

        if (!_.isEmpty(result.rows)) {
            let teamName = result.rows[0].team_name,
                gender   = result.rows[0].gender;

            return Db.query(`
            SELECT rt.team_name, rt.gender
            FROM roster_team rt
            LEFT JOIN master_team ma
                ON ma.master_team_id = rt.master_team_id 
                AND ma.deleted IS NULL
                AND ma.season = $1
            WHERE rt.team_name = $2
                AND rt.roster_team_id <> $3
                AND rt.gender = $4
                AND rt.event_id = $5
                AND rt.deleted IS NULL
            `, [season, teamName, rosterTeamId, gender, eventId]
            ).then(duplicates => {
                if (!_.isEmpty(duplicates.rows)) {
                    errors.push('Team with such name is already exists within the event');
                }
                return errors.length?errors:undefined;
            })

        } else {
            throw {
                validation: 'Event or Team not found'
            }
        };

    })
}

function __rosterQtyValidator (eventId, rosterTeamId, validationRules) {
    let athletesMinAccept           = validationRules.athletesMinAccept,
        athletesMaxAccept           = validationRules.athletesMaxAccept,
        staffersMaxAccept           = validationRules.staffersMaxAccept,
        athletesMinEnter            = validationRules.athletesMinEnter,
        athletesMaxEnter            = validationRules.athletesMaxEnter,
        primaryStaffersMaxAccept    = validationRules.primaryStaffersMaxAccept,
        athletesMaxCheckin          = validationRules.athletesMaxCheckin,
        athletesMinCheckin          = validationRules.athletesMinCheckin;

    return Db.query(`
          SELECT COALESCE(NULLIF(d.athletes_maxcount, 0), $3::NUMERIC) "athletes_maxcount",
	      COALESCE(NULLIF(d.athletes_maxcount_checkin, 0), $4::NUMERIC) "athletes_maxcount_checkin",
	      (SELECT COUNT(ra.*)
	              FROM "roster_athlete" ra
              INNER JOIN "master_athlete" ma 
                  ON ma.master_athlete_id = ra.master_athlete_id
                  AND ma.season = $5
              WHERE ra.roster_team_id = rt.roster_team_id
                  AND ra.deleted IS NULL
                  AND ra.deleted_by_user IS NULL
                  AND (ra."as_staff" = 0 OR ra."as_staff" IS NULL)
                  AND ra.event_id = rt.event_id
                  AND mt.master_club_id = ma.master_club_id
          ) "athletes_count", (
                SELECT COUNT(rsr.*)
                FROM (
                    SELECT r."roster_staff_role_id", r."deleted",
                        r.deleted_by_user, COALESCE(r.primary, msr.primary) "primary",
                        r.roster_team_id
                    FROM "roster_staff_role" r 
                    LEFT JOIN "master_staff_role" msr 
                        ON msr.master_team_id = r.master_team_id
                        AND msr.master_staff_id = r.master_staff_id
                    INNER JOIN "master_staff" ms 
                      ON ms.master_staff_id = r.master_staff_id 
                      AND ms.season = $5
                    WHERE r.roster_team_id = $2
                    UNION ALL 
                    SELECT a."roster_athlete_id" "roster_staff_role_id", a.deleted,
                        a.deleted_by_user, (a."as_staff" = 1) "primary", a.roster_team_id
                    FROM "roster_athlete" a 
                    INNER JOIN "master_athlete" ma 
                      ON ma.master_athlete_id = a.master_athlete_id
                      AND ma.season = $5
                    WHERE a."as_staff" > 0
                      AND a.roster_team_id = $2
                      AND a.deleted IS NULL 
                      AND a.deleted_by_user IS NULL
                ) rsr
                WHERE rsr.roster_team_id = rt.roster_team_id
                    AND rsr.deleted IS NULL
                    AND rsr.deleted_by_user IS NULL
                    AND rsr.primary IS TRUE
          ) "primary_staff_count", (
                SELECT COUNT(rsr.*)
                FROM (
                    SELECT r."roster_staff_role_id", r."deleted",
                        r.deleted_by_user,
                        r.roster_team_id
                    FROM "roster_staff_role" r 
                    LEFT JOIN "master_staff_role" msr 
                        ON msr.master_team_id = r.master_team_id
                        AND msr.master_staff_id = r.master_staff_id
                    INNER JOIN "master_staff" ms 
                      ON ms.master_staff_id = r.master_staff_id 
                      AND ms.season = $5
                    WHERE r.roster_team_id = $2
                    UNION ALL 
                    SELECT a."roster_athlete_id" "roster_staff_role_id", a.deleted,
                        a.deleted_by_user, a.roster_team_id
                    FROM "roster_athlete" a 
                    INNER JOIN "master_athlete" ma 
                      ON ma.master_athlete_id = a.master_athlete_id
                      AND ma.season = $5
                    WHERE a."as_staff" > 0
                      AND a.roster_team_id = $2
                      AND a.deleted IS NULL 
                      AND a.deleted_by_user IS NULL
                ) rsr
                WHERE rsr.roster_team_id = rt.roster_team_id
                    AND rsr.deleted IS NULL
                    AND rsr.deleted_by_user IS NULL
          ) "staff_count",
          e.online_team_checkin_available IS TRUE "online_checkin_mode_enabled"
        FROM "roster_team" rt
        JOIN event e ON e.event_id = rt.event_id 
        INNER JOIN "division" d 
          ON d.division_id = rt.division_id 
          AND rt.event_id = rt.event_id
        INNER JOIN "master_team" mt 
           ON mt.master_team_id = rt.master_team_id
           AND mt.season = $5
           AND mt.deleted IS NULL
        WHERE rt.event_id = $1
            AND rt.roster_team_id  = $2`,
        [eventId, rosterTeamId, athletesMaxAccept, athletesMaxCheckin, sails.config.sw_season.current]
    ).then(function (result) {
        let event   = _.first(result.rows),
            errors  = [];

        if (_.isEmpty(event)) {
            throw {
                validation: 'Event or Team not found'
            }
        }

        let teamAthletesQty             = Number(event.athletes_count)              || 0,
            teamStaffersQty             = Number(event.staff_count)                 || 0,
            teamPrimaryStaffersQty      = Number(event.primary_staff_count)         || 0;

        let athletes_maxcount           = Number(event.athletes_maxcount)           || 0;
        let athletes_maxcount_checkin   = Number(event.athletes_maxcount_checkin)   || 0;


        if(athletesMinEnter) {
            if(teamAthletesQty < athletesMinEnter) {
                errors.push(`Minimum Players Count for entry allowed is ${athletesMinEnter}, Team has ${teamAthletesQty}`);
            }
        }

        if(athletesMaxEnter) {
            if(teamAthletesQty > athletesMaxEnter) {
                errors.push(`Maximum Players Count for entry allowed is ${athletesMaxEnter}, Team has ${teamAthletesQty}`);
            }
        }

        if(athletesMinAccept) {
            if(teamAthletesQty < athletesMinAccept) {
                errors.push(`Minimum Players Count allowed is ${athletesMinAccept}, Team has ${teamAthletesQty}`);
            }
        }

        if(athletes_maxcount) {
            if(teamAthletesQty > athletes_maxcount) {
                errors.push(`Maximum Players Count allowed is ${athletes_maxcount}, Team has ${teamAthletesQty}`);
            }
        }

        if(athletesMinCheckin) {
            if(athletesMinCheckin > teamAthletesQty) {
                errors.push(`Minimum Players Count for checkin allowed is ${athletesMinCheckin}, Team has ${teamAthletesQty}`);
            }
        }

        if(athletes_maxcount_checkin) {
            if(teamAthletesQty > athletes_maxcount_checkin) {
                errors.push(`Maximum Players Count allowed is ${athletes_maxcount_checkin} in CheckIn, Team has ${teamAthletesQty}`);
            }
        }

        if(staffersMaxAccept) {
          if(teamStaffersQty > staffersMaxAccept) {
            errors.push(`Maximum Staff Count allowed is ${staffersMaxAccept}, Team has ${teamStaffersQty}`);
          }
        }

        if(primaryStaffersMaxAccept) {
            if(teamPrimaryStaffersQty > primaryStaffersMaxAccept) {
                errors.push(`Maximum Primary Staff Count allowed is ${primaryStaffersMaxAccept}, Team has ${teamPrimaryStaffersQty}`);
            }
        }

        if(teamStaffersQty === 0) {
          errors.push('Team should have at least one Primary Staffer');
        }

        return errors.length?errors:undefined;
    })
}

function __birthDateValidator (eventId, rosterTeamId) {
  return Db.query(
      `SELECT
           FORMAT('%s %s', ma.first, ma.last) "name", 'Athlete'::TEXT "role"
       FROM "roster_athlete" ra 
       INNER JOIN "master_athlete" ma 
           ON ma.master_athlete_id = ra.master_athlete_id
           AND ma.season = $3
       WHERE ra.roster_team_id = $2
           AND ra.event_id = $1
           AND ra.deleted_by_user IS NULL
           AND ra.deleted IS NULL
           AND (ra."as_staff" = 0 OR ra."as_staff" IS NULL)
           AND ma.birthdate IS NULL
       UNION ALL
       SELECT 
           FORMAT('%s %s', ms.first, ms.last) "name",
           'Staff'::TEXT "role"
       FROM "roster_staff_role" rsr
       INNER JOIN "roster_team" rt 
            ON rt.roster_team_id = rsr.roster_team_id 
            AND rt.event_id = $1
        INNER JOIN "master_staff" ms 
            ON ms.master_staff_id = rsr.master_staff_id
            AND ms.birthdate IS NULL
            AND ms.season = $3
        INNER JOIN "master_staff_role" msr 
            ON msr.master_staff_id = rsr.master_staff_id
            AND msr.master_team_id = rsr.master_team_id
        LEFT JOIN "role" rr 
            ON rr.role_id = rsr.role_id
        LEFT JOIN "role" mr 
            ON mr.role_id = msr.role_id
        WHERE rsr.roster_team_id = $2
            AND rsr.deleted IS NULL
            AND rsr.deleted_by_user IS NULL
        ORDER BY "role"`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
  ).then(function (result) {
      if(result.rowCount === 0)
        return;
      return result.rows;
  });
}

function __safeSportValidator (eventId, rosterTeamId) {
    return Db.query(
        `SELECT
             FORMAT(
                 '%s %s%s',
                 ms.first,
                 ms.last,
                 CASE
                     WHEN ms.organization_code IS NOT NULL THEN FORMAT(' (%s)', ms.organization_code)
                     ELSE ''
                     END
             ) AS name
         FROM "roster_staff_role" rsr 
         INNER JOIN "roster_team" rt 
             ON rt.roster_team_id = rsr.roster_team_id 
             AND rt.event_id = $1
          LEFT JOIN "master_staff_role" msr
              ON msr.master_team_id = rsr.master_team_id
              AND msr.master_staff_id = rsr.master_staff_id
          INNER JOIN master_staff ms
              ON ms.master_staff_id = rsr.master_staff_id
         WHERE rsr.roster_team_id = $2
             AND rsr.deleted IS NULL
             AND rsr.deleted_by_user IS NULL
             AND (
                ms."safesport_statusid" IS NULL 
                OR ms."safesport_statusid" <> '2'
             )
             AND ms.season = $3`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then(function (result) {
        if(result.rowCount === 0)
          return;
        return result.rows;
    });
}

function __assignedRoleValidator (eventId, rosterTeamId) {
    return Db.query(
        `SELECT
             FORMAT('%s %s', ms.first, ms.last) "name"
         FROM "roster_staff_role" rsr 
         INNER JOIN "roster_team" rt 
             ON rt.roster_team_id = rsr.roster_team_id 
             AND rt.event_id = $1
         INNER JOIN "master_staff" ms 
             ON ms.master_staff_id = rsr.master_staff_id
         LEFT JOIN "master_staff_role" msr 
             ON msr.master_staff_id = rsr.master_staff_id
             AND msr.master_team_id = rsr.master_team_id
         WHERE rsr.roster_team_id = $2
             AND rsr.deleted IS NULL
             AND rsr.deleted_by_user IS NULL
             AND (rsr.role_id IS NULL OR rsr.role_id = 0)
             AND (msr.role_id IS NULL OR msr.role_id = 0)
             AND ms.season = $3`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then(function (result) {
        if(result.rowCount === 0)
          return;
        return result.rows;
    });
}

function __bgScreeningValidator (eventId, rosterTeamId) {
    return Db.query(
        `SELECT
             FORMAT(
                 '%s %s%s',
                 ms.first,
                 ms.last,
                 CASE
                     WHEN ms.organization_code IS NOT NULL THEN FORMAT(' (%s)', ms.organization_code)
                     ELSE ''
                     END
             ) AS name
         FROM "roster_staff_role" rsr 
         INNER JOIN "roster_team" rt 
             ON rt.roster_team_id = rsr.roster_team_id 
             AND rt.event_id = $1
         INNER JOIN "master_staff" ms 
             ON ms.master_staff_id = rsr.master_staff_id
         WHERE rsr.roster_team_id = $2
             AND rsr.deleted IS NULL
             AND rsr.deleted_by_user IS NULL
             AND (
                CASE
                    WHEN ms.bg_screening = 'foreign' THEN FALSE
                    WHEN ms.bg_expire_date IS NOT NULL
                        THEN (
                                ms.bg_expire_date::DATE <= CURRENT_DATE 
                                OR ms.bg_screening <> '2' 
                                OR ms.bg_screening IS NULL
                              )
                    WHEN ms.bg_expire_date IS NULL
                        THEN (ms.bg_screening <> '2' OR ms.bg_screening IS NULL)
                END
             ) IS TRUE
             AND ms.season = $3`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then(function (result) {
        if(result.rowCount === 0)
          return;
        return result.rows;
    });
}
// Head Coach must have cell phone number
function __hasHeadCoachCellPhone (eventId, rosterTeamId) {
    return Db.query(
        `SELECT
             FORMAT(
                 '%s %s%s',
                 INITCAP(ms.first),
                 INITCAP(ms.last),
                 CASE
                     WHEN ms.organization_code IS NOT NULL THEN FORMAT(' (%s)', ms.organization_code)
                     ELSE ''
                     END
             ) AS name
         FROM "roster_staff_role" rsr 
         INNER JOIN "roster_team" rt 
             ON rt.roster_team_id = rsr.roster_team_id 
             AND rt.event_id = $1
         INNER JOIN "master_staff" ms 
             ON ms.master_staff_id = rsr.master_staff_id
         LEFT JOIN "master_staff_role" msr 
             ON msr.master_staff_id = rsr.master_staff_id
             AND msr.master_team_id = rsr.master_team_id
         WHERE rsr.roster_team_id = $2
             AND rsr.deleted IS NULL
             AND rsr.deleted_by_user IS NULL
             AND (ms.phone IS NULL OR ms.phone = '')
             AND COALESCE(rsr.role_id, msr.role_id) = 4
             AND ms.season = $3`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then(function (result) {
        if(result.rowCount === 0)
          return;
        return result.rows;
    });
}

// Primary staffer must have phone and email
function __primaryStaffersWithoutPhoneOrEmail (eventID, rosterTeamID) {
    let query =
        `SELECT DISTINCT 
            FORMAT(
                    '%s %s%s',
                    INITCAP(ms.first),
                    INITCAP(ms.last),
                    CASE
                        WHEN ms.organization_code IS NOT NULL THEN FORMAT(' (%s)', ms.organization_code)
                        ELSE ''
                        END
                ) AS name,
            (ms.phone IS NULL) AS "has_no_phone", (ms.email IS NULL) AS "has_no_email"
        FROM roster_team rt
          LEFT JOIN roster_staff_role rsr
            ON rt.roster_team_id = rsr.roster_team_id
               AND rsr.deleted IS NULL
               AND rsr.deleted_by_user IS NULL
          LEFT JOIN master_staff ms
            ON ms.master_staff_id = rsr.master_staff_id
          LEFT JOIN master_staff_role msr
            ON msr.master_staff_id = rsr.master_staff_id
        WHERE rt.roster_team_id = $1
              AND rt.event_id = $2
              AND COALESCE(rsr."primary", msr."primary") IS TRUE
              AND (ms.phone IS NULL OR ms.email IS NULL)
              AND rt.deleted IS NULL`;

    return Db.query(query, [rosterTeamID, eventID]).then(result => {
        if(result.rowCount === 0)
            return;
        return result.rows;
    });
}

function filterValidatedByEOTeams(team, eventID) {
    let query = squel.select().from('roster_team', 'rt')
        .field('rt.roster_team_id')
        .where('rt.event_id = ?', eventID)
        .where(`rt.roster_validated_by <> ? OR rt.roster_validated_by IS NULL`,
            CheckInRosterService.ROSTER_VALIDATION_SOURCE.EO
        )
        .where('rt.roster_team_id = ?', team);

    return Db.query(query).then(result => result.rows || [])
        .then(filteredIds => {
            if(_.isEmpty(filteredIds)) {
                return null;
            }

            return filteredIds[0].roster_team_id;
        })
}

function __teamHasNotOneHeadCoach(eventId, rosterTeamId) {
    return Db.query(
        `SELECT count(rsr.*) 
        FROM roster_staff_role rsr
             INNER JOIN "roster_team" rt 
                 ON rt.roster_team_id = rsr.roster_team_id 
                 AND rt.event_id = $1
             INNER JOIN "master_staff" ms 
                 ON ms.master_staff_id = rsr.master_staff_id
             LEFT JOIN "master_staff_role" msr 
                 ON msr.master_staff_id = rsr.master_staff_id
                 AND msr.master_team_id = rsr.master_team_id
             WHERE rsr.roster_team_id = $2
                 AND rsr.deleted IS NULL
                 AND rsr.deleted_by_user IS NULL
                 AND COALESCE(NULLIF(rsr.role_id, '0'), msr.role_id) = 4
                 AND ms.season = $3`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then( result => {
        if(Number(result.rows[0].count) !== 1) {
            return result.rows[0].count;
        }

        return;
    })
}

function __checkHCUniquePerEvent(eventId, rosterTeamId) {
    return Db.query(
        `
        WITH event_master_staffs AS (
            SELECT rt.roster_team_id, FORMAT('%s (%s)', rt.team_name, rt.organization_code) AS team_name, 
                    ms.usav_number, ms.first, ms.last, ms.birthdate, 
                    ms.aau_membership_id,
                    FORMAT(
                       '%s %s%s',
                       INITCAP(ms.first),
                       INITCAP(ms.last),
                       CASE
                           WHEN ms.organization_code IS NOT NULL THEN FORMAT(' (%s)', ms.organization_code)
                           ELSE ''
                           END
                    ) AS "name"
                FROM roster_team rt
                INNER JOIN roster_staff_role AS rsr ON rt.roster_team_id = rsr.roster_team_id 
                INNER JOIN master_staff AS ms ON ms.master_staff_id = rsr.master_staff_id
                LEFT JOIN "master_staff_role" AS msr ON msr.master_staff_id = rsr.master_staff_id
                    AND msr.master_team_id = rsr.master_team_id
                WHERE rt.event_id = $1
                    AND rt.status_entry <> 11
                    AND rsr.deleted IS NULL
                    AND rsr.deleted_by_user IS NULL
                    AND COALESCE(NULLIF(rsr.role_id, '0'), msr.role_id) = 4
                    AND ms.season = $3
        ), roster_team_staffs AS (
            SELECT * FROM event_master_staffs AS ems
                WHERE ems.roster_team_id = $2
        )
        SELECT ems.name, json_agg(json_build_object('name', ems.team_name)) as dups FROM event_master_staffs AS ems, roster_team_staffs as rts
            WHERE 
                (rts.usav_number = ems.usav_number OR rts.aau_membership_id = ems.aau_membership_id) 
                AND rts.first = ems.first
                AND rts.last = ems.last AND rts.birthdate = ems.birthdate
            GROUP BY ems.name, ems.usav_number, ems.first, ems.last, ems.birthdate
            HAVING COUNT(ems) > 1
        `,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then( ({ rows }) => {
        if(!_.isEmpty(rows)){
            return rows
        };

        return;
    })
}

// Cell phone number for other staff members, Cell numbers must be unique from Head Coach
function __staffCellPhone (eventId, rosterTeamId) {
    return Db.query(
        `SELECT 
             "coach"."name", (
                 CASE 
                     WHEN (
                         "coach"."head_coach_phone" IS NOT NULL and "coach"."head_coach_phone" <> ''
                         AND ("coach"."phone" = "coach"."head_coach_phone")
                     ) THEN TRUE
                     ELSE FALSE
                 END 
             ) "same"
         FROM (
              SELECT
                  FORMAT(
                      '%s %s%s',
                      INITCAP(ms.first),
                      INITCAP(ms.last),
                      CASE
                          WHEN ms.organization_code IS NOT NULL THEN FORMAT(' (%s)', ms.organization_code)
                          ELSE ''
                          END
                  ) AS "name",
                 ms.phone, "head_coach".phone "head_coach_phone"
              FROM "roster_staff_role" rsr 
              INNER JOIN "roster_team" rt 
                  ON rt.roster_team_id = rsr.roster_team_id 
                  AND rt.event_id = $1
              INNER JOIN (
                 SELECT ms.phone
                 FROM "roster_staff_role" rsr
                 LEFT JOIN "master_staff_role" msr 
                     ON msr.master_staff_id = rsr.master_staff_id
                     AND msr.master_team_id = rsr.master_team_id
                 INNER JOIN "master_staff" ms 
                     ON ms.master_staff_id = rsr.master_staff_id
                     AND ms.season = $3
                 WHERE rsr.roster_team_id = $2
                     AND COALESCE(rsr.role_id, msr.role_id) = 4
                     AND rsr."deleted" IS NULL
                 LIMIT 1
              ) "head_coach"
              ON TRUE
              INNER JOIN "master_staff" ms 
                  ON ms.master_staff_id = rsr.master_staff_id
              LEFT JOIN "master_staff_role" msr 
                  ON msr.master_staff_id = rsr.master_staff_id
                  AND msr.master_team_id = rsr.master_team_id
              WHERE rsr.roster_team_id = $2
                  AND rsr.deleted IS NULL
                  AND rsr.deleted_by_user IS NULL
                  AND (
                     COALESCE(rsr.role_id, msr.role_id) <> 4
                     OR COALESCE(rsr.role_id, msr.role_id) IS NULL
                  )
                  AND ms.season = $3
         ) "coach"
          WHERE "coach"."phone" IS NULL OR "coach"."phone" = ''
             OR (
                  "coach"."head_coach_phone" IS NOT NULL 
                  AND ("coach"."phone" = "coach"."head_coach_phone")
             )`,
        [eventId, rosterTeamId, sails.config.sw_season.current]
    ).then(function (result) {
        if(result.rowCount === 0)
          return;
        return result.rows;
    });
}

async function __validateTeamPlayersUSAVMembershipStatus (eventId, rosterTeamId) {
    const query =
        `SELECT FORMAT('%s %s', INITCAP(ma.first), INITCAP(ma.last)) AS name,
                INITCAP((coalesce(ma.membership_status, 'cancelled'))) AS membership_status
        FROM roster_team rt
                 JOIN master_team mt ON mt.master_team_id = rt.master_team_id AND mt.deleted IS NULL
                 JOIN roster_athlete ra
                      ON ra.roster_team_id = rt.roster_team_id AND ra.deleted IS NULL AND ra.deleted_by_user IS NULL AND
                         ra.event_id = rt.event_id
                 JOIN master_athlete ma
                      ON ma.master_athlete_id = ra.master_athlete_id AND ma.master_team_id = mt.master_team_id
        WHERE rt.event_id = $1
          AND rt.roster_team_id = $2
          AND rt.deleted IS NULL
          AND (ma.membership_status IS NULL OR ma.membership_status <> $3)`;

    const { rowCount, rows } = await Db.query(query, [eventId, rosterTeamId, SEUtils.ELIGIBLE_MEMBER_STATUS]);

    if(rowCount > 0) {
        return rows;
    }
}

async function __validateTeamStaffersUSAVMembershipStatus (eventId, rosterTeamId) {
    const query =
        `SELECT FORMAT('%s %s', INITCAP(ms.first), INITCAP(ms.last)) AS name,
                INITCAP((coalesce(ms.membership_status, 'cancelled'))) AS membership_status
        FROM roster_team rt
                 JOIN master_team mt ON mt.master_team_id = rt.master_team_id AND mt.deleted IS NULL
                 JOIN roster_staff_role rsr
                      ON rsr.roster_team_id = rt.roster_team_id AND rsr.deleted IS NULL AND rsr.deleted_by_user IS NULL
                 JOIN master_staff_role msr
                      ON msr.master_staff_id = rsr.master_staff_id AND msr.master_team_id = rsr.master_team_id
                 JOIN master_staff ms ON ms.master_staff_id = msr.master_staff_id
        WHERE rt.event_id = $1
          AND rt.roster_team_id = $2
          AND rt.deleted IS NULL
          AND (ms.membership_status IS NULL OR ms.membership_status <> $3)`;

    const { rowCount, rows } = await Db.query(query, [eventId, rosterTeamId, SEUtils.ELIGIBLE_MEMBER_STATUS]);

    if(rowCount > 0) {
        return rows;
    }
}

function __primaryStaffCheck (eventId, rosterTeamId) {
    return Db.query(
        `SELECT "d".* FROM (
            SELECT 
                FORMAT('%s %s', ms.first, ms.last) "name", (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("pr"))), '[]'::JSON)
                    FROM (
                        SELECT FORMAT('%s (%s)', t.team_name, t.organization_code) "name"
                        FROM "roster_staff_role" r 
                        INNER JOIN "roster_team" t 
                            ON t.roster_team_id = r.roster_team_id
                            AND t.event_id = rt.event_id
                            AND t.status_entry <> ${ENTRY_STATUSES.DECLINED}
                        LEFT JOIN "master_staff_role" msr 
                            ON msr.master_staff_id = r.master_staff_id
                            AND msr.master_team_id = t.master_team_id
                        WHERE r.master_staff_id = ms.master_staff_id
                            AND r.deleted IS NULL
                            AND r.deleted_by_user IS NULL
                            AND COALESCE(r.primary, msr.primary) IS TRUE
                    ) "pr"
                ) "primaries"
            FROM "roster_staff_role" rsr
            LEFT JOIN "master_staff_role" msr
                ON msr.master_team_id = rsr.master_team_id
                AND msr.master_staff_id = rsr.master_staff_id
            INNER JOIN "roster_team" rt 
                ON rt.roster_team_id = rsr.roster_team_id
                AND rt.event_id = $2
            INNER JOIN "master_staff" ms 
                ON ms.master_staff_id = rsr.master_staff_id
                AND ms.season = $3
            WHERE rsr."roster_team_id" = $1
                AND rsr.deleted IS NULL
                AND rsr.deleted_by_user IS NULL
        ) "d" 
        WHERE JSON_ARRAY_LENGTH("d".primaries) <> 1`,
        [rosterTeamId, eventId, sails.config.sw_season.current]
    ).then(function (result) {
        if(result.rowCount === 0)
          return;
        return result.rows;
    });
}

var MEMBERS_SQL =
`SELECT 
      rt.team_name, rt.gender, rc.club_name, rt.organization_code "code", 
      COALESCE(rt.wristbands_count_athletes, 0) "athletes_count",
      COALESCE(rt.wristbands_count_staff, 0) "staff_count",
      e.online_team_checkin_mode,
      d.name "division_name", TO_CHAR(rt.date_entered, 'MM/DD/YYYY') "date_entered", ( 
          SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(athetes_and_staff))), '[]'::JSON)
          FROM ( 
               SELECT 
                   ma.first, ma.last, (
                      CASE 
                          WHEN (ra."as_staff" = 0) 
                              THEN 'athlete' 
                          ELSE 'staff' 
                      END
                   ) "role", ma.gender, '-' "cert", 
                   ma.gradyear, ma.phonem, 
                   COALESCE(ra.jersey, ma.jersey) "jersey",
                   COALESCE(ra.aau_jersey, ma.aau_jersey) "aau_jersey",
                   (
                      CASE 
                          WHEN (ra."as_staff" > 0)
                            THEN 'Staff'
                          ELSE COALESCE(spr.short_name, spm.short_name)
                      END 
                   ) "sport_position", 
                   (ra.as_staff = 1) "primary",
                   ma.phoneh, ma.phonep, ma.email, ma.age, ma.address, 
                   ma.state, ma.city, ma.zip, ma.organization_code "code", ma.aau_membership_id "aau_code",
                   NULL "bkg", NULL::INTEGER "sort_order",
                   TO_CHAR(ma.birthdate, 'MM/DD/YYYY') "birthdate", ra.as_staff, 
                   (
                    CASE
                        WHEN date_part('year', age(e.date_end::DATE, ma.birthdate))::INTEGER >= $3
                            THEN 
                                (CASE 
                                    WHEN (ma.safesport_statusid <> $2 OR ma.safesport_statusid IS NULL) THEN 'NO'
                                    ELSE 'YES'
                                END)
                        ELSE ''
                    END
                   ) "safesport_statusid",
                   -- check if referee certificate is expired
                   CASE 
                        WHEN COALESCE(NULLIF(ma.ref_end_date, '')::DATE, '19700101') > e.date_end THEN 
                            COALESCE (wcn1.cert_short_name, CASE WHEN ma.ref_cert_name IS NOT NULL THEN 'YES' ELSE '-' END)
                        ELSE '-'
                   END "ref",
                   -- check if score keeping certificate is expired
                   CASE 
                        WHEN COALESCE(NULLIF(ma.score_end_date, '')::DATE, '19700101') > e.date_end THEN 
                            COALESCE (wcn2.cert_short_name, CASE WHEN ma.score_cert_name IS NOT NULL THEN 'YES' ELSE '-' END) 
                        ELSE '-'
                   END "score"
               FROM roster_athlete ra 
               LEFT JOIN master_athlete ma
                   ON ma.master_athlete_id = ra.master_athlete_id
               LEFT JOIN sport_position spr 
                   ON spr.sport_position_id = ra.sport_position_id
               LEFT JOIN "sport_position" spm 
                   ON spm.sport_position_id = ma.sport_position_id
               LEFT JOIN "webpoint_cert_name" wcn1
                   ON wcn1.wp_name = 'REFCERTNAME' and wcn1.cert_name = ma.ref_cert_name
               LEFT JOIN "webpoint_cert_name" wcn2
                   ON wcn2.wp_name = 'SCORECERTNAME' and wcn2.cert_name = ma.score_cert_name
               WHERE ra.roster_team_id = rt.roster_team_id
                    AND ra.deleted IS NULL
                    AND ra.event_id = rt.event_id
                    AND ra.deleted_by_user IS NULL
               UNION 
               SELECT
                    ms.first, ms.last, 'staff' "role", ms.gender, 
                    (
                        CASE 
                          WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                          WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                          ELSE NULL
                        END
                    ) "cert",
                    -1 "gradyear", ms.phone "phonem",
                    NULL::INTEGER "jersey",
                    NULL::INTEGER "aau_jersey",
                    string_agg(DISTINCT COALESCE(r.short_name, mr.short_name), ', ') "sport_position",
                    COALESCE(rsr.primary, msr.primary) "primary",
                    ms.phoneh, NULL "phonep", ms.email, NULL::INTEGER "age", ms.address,
                    ms.state, ms.city, ms.zip, ms.organization_code "code", ms.aau_membership_id "aau_code",
                    NULLIF(ms.bg_screening, '') "bkg", r.sort_order,
                    TO_CHAR(ms.birthdate, 'MM/DD/YYYY') "birthdate", NULL::INTEGER "as_staff", (
                      CASE
                        WHEN (ms.safesport_statusid = $2) THEN 'YES'
                        ELSE 'NO'
                      END
                    ) safesport_statusid,
                    -- check if referee certificate is expired
                    CASE 
                        WHEN COALESCE(NULLIF(ms.ref_end_date, '')::DATE, '19700101') > e.date_end THEN 
                            COALESCE (wcn1.cert_short_name, CASE WHEN ms.ref_cert_name IS NOT NULL THEN 'YES' ELSE '-' END)
                        ELSE '-'
                    END "ref",
                    -- check if score keeping certificate is expired
                    CASE 
                        WHEN COALESCE(NULLIF(ms.score_end_date, '')::DATE, '19700101') > e.date_end THEN 
                            COALESCE (wcn2.cert_short_name, CASE WHEN ms.score_cert_name IS NOT NULL THEN 'YES' ELSE '-' END) 
                        ELSE '-'
                    END "score"        
                FROM roster_staff_role rsr
                LEFT JOIN "master_staff_role" msr
                   ON msr.master_team_id = rsr.master_team_id
                   AND msr.master_staff_id = rsr.master_staff_id
                LEFT JOIN master_staff ms
                   ON ms.master_staff_id = rsr.master_staff_id
                LEFT JOIN "role" r
                   ON r.role_id = rsr.role_id
                LEFT JOIN "role" mr
                   ON mr.role_id = msr.role_id
                LEFT JOIN "webpoint_cert_name" wcn1
                    ON wcn1.wp_name = 'REFCERTNAME' and wcn1.cert_name = ms.ref_cert_name
                LEFT JOIN "webpoint_cert_name" wcn2
                    ON wcn2.wp_name = 'SCORECERTNAME' and wcn2.cert_name = ms.score_cert_name
                WHERE rsr.roster_team_id = rt.roster_team_id
                   AND rsr.deleted IS NULL
                   AND rsr.deleted_by_user IS NULL
                GROUP BY rsr.master_staff_id, ms.first, ms.last,"role", ms.gender, 
                    "cert", ms.is_impact, "gradyear", "phonem", "jersey", rsr."primary",
                    msr."primary", ms.phoneh, "phonep", ms.email, "age", ms.address,
                    ms.state, ms.city, ms.zip, "code", "aau_code", "bkg", r.sort_order, "birthdate",
                    "as_staff", safesport_statusid, "ref", "score"
                ORDER BY "role", "primary" DESC, "sort_order", "jersey"
           ) athetes_and_staff 
    ) "members", (
        SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("ch"))), '[]'::JSON)
        FROM (
            SELECT 
                TO_CHAR(otch."created"::TIMESTAMPTZ AT TIME ZONE e."timezone", 'Mon DD, YYYY, HH12:MI AM') "created",
                FORMAT('%s %s', ms.first, ms.last) "name",
                ms."phone"
            FROM "event_team_checkin" otch
            INNER JOIN "master_staff" ms 
                ON ms."master_staff_id" = otch."master_staff_id"
                AND ms."deleted" IS NULL
            WHERE otch."roster_team_id" = rt."roster_team_id"
                AND otch."event_id" = rt."event_id"
            ORDER BY otch."staff_type"
        ) "ch"
    ) "checkin" 
   FROM roster_team rt 
   INNER JOIN "event" e 
      ON e."event_id" = rt."event_id"
   LEFT JOIN master_team mt 
       ON rt.master_team_id = mt.master_team_id 
   LEFT JOIN roster_club rc 
       ON rc.roster_club_id = rt.roster_club_id  
   LEFT JOIN division d 
       ON rt.division_id = d.division_id 
   WHERE rt.event_id = $1
    AND rt.deleted IS NULL
   {0}`;
