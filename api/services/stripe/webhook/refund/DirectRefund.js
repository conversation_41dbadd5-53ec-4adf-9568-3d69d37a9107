
const CustomPaymentRefund = require('./direct/CustomPaymentRefund');

class DirectRefund {
    constructor (webhookData) {
        this.webhook = webhookData;
    }

    async getPurchaseData (chargeID) {
        let query = knex('stripe_payment_intent AS spi')
            .columns(
                'cp.event_id',
                'cp.payment_for',
                'cp.amount',
                'cp.custom_payment_id AS purchase_id',
                knex.raw(`
                    (CASE
                        WHEN (spi.stripe_percent > 0)
                          THEN ROUND(spi.stripe_percent / 100, 4)
                        ELSE 0
                    END) AS stripe_percent
                `),
                'spm.type AS payment_method_type',
                knex.raw(`(spi.amount - cp.amount)::NUMERIC AS amount_refunded`)
            )
            .join('custom_payment AS cp', 'spi.stripe_payment_intent_id', 'cp.stripe_payment_intent_id')
            .join('stripe_payment_method AS spm', 'spm.stripe_payment_method_id', 'cp.stripe_payment_method_id')
            .where('spi.stripe_charge_id', chargeID)

        this.purchase = await Db.query(query).then(result => result && result.rows[0]);

        return this.purchase;
    }

    async process (refundAmount) {
        let RefundType = this.__getRefundTypeInstance();

        return RefundType.proceed(refundAmount);
    }

    __getRefundTypeInstance () {
        if(_.isEmpty(this.purchase)) {
            throw { text: 'Purchase is empty' };
        }

        return new CustomPaymentRefund(this.purchase);
    }
}

module.exports = DirectRefund;
