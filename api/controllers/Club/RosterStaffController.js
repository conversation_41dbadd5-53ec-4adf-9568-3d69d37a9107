'use strict';

var updater     = require('../../lib/SavesportLoader');

module.exports = {   
    // get /api/club/event/:event/staff/:staff/info
    findRosterStaff: function (req, res) {
        var $id                 = parseInt(req.params.staff, 10),
            $event_id           = parseInt(req.params.event, 10),
            $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10);
        
        if(!$id) 
            return res.validation('Invalid athlete identifier passed');
        if(!$event_id) 
            return res.validation('Invalid event identifier passed');
        if(!$master_club_id) 
            return res.forbidden('No club found. Forbidden');

        Db.query(
            `SELECT  
                ms.master_staff_id, COALESCE(ms.membership_status, 'N/A') "membership_status", 
                ms.first, ms.last, ms.organization_code "usav_code", INITCAP(ms.gender::TEXT) "gender", 
                TO_CHAR(ms.birthdate, 'MM/DD/YYYY') "birthdate", ms.cert, (
                    CASE
                        WHEN ms.bg_expire_date IS NOT NULL
                            THEN TO_CHAR(ms.bg_expire_date, 'MM/DD/YYYY') 
                        ELSE 'N/A'
                    END
                ) "bg_expire_date", ( 
                     CASE 
                       WHEN (ms.safesport_statusid = '2') THEN 'OK' 
                       ELSE ''
                     END 
                ) "ss_status", 
                ms.deleted IS NOT NULL AS "deleted_from_club",

                rsr.roster_staff_role_id, rsr.role_id, COALESCE(rr.name, 'N/A') "role_name", 
                COALESCE(rsr.primary, msr.primary) "primary", 

                msr.master_staff_role_id, msr.role_id "default_role_id", rt.locked,
                    COALESCE(mr.name, 'N/A') "default_role_name", msr.primary "default_primary", 

                (
                    CASE
                        WHEN rsr.deleted_by_user IS NOT NULL 
                            THEN TO_CHAR((rsr."deleted_by_user" AT TIME ZONE e.timezone), 'Mon DD, YYYY, HH12:MI AM') 
                        ELSE NULL
                    END
                ) "deleted_by_user"
            FROM "roster_staff_role" rsr 
            LEFT JOIN "master_staff_role" msr 
                ON msr.master_staff_id = rsr.master_staff_id
                AND msr.master_team_id = rsr.master_team_id
            INNER JOIN roster_team rt 
                ON rt.roster_team_id = rsr.roster_team_id 
            INNER JOIN "event" e 
                ON rt.event_id = e.event_id
            INNER JOIN master_staff ms 
                ON ms.master_staff_Id = rsr.master_staff_id 
            INNER JOIN master_team mt 
                ON mt.master_team_id = rsr.master_team_id 
                AND mt.master_club_id = $3 
            LEFT JOIN "role" rr 
                ON rr.role_id = rsr.role_id
            LEFT JOIN "role" mr 
                ON mr.role_id = msr.role_id
            WHERE rsr.roster_staff_role_id = $1 
                AND rsr.deleted is null 
                AND rt.event_id = $2`, 
            [$id, $event_id, $master_club_id]
        ).then(result => {
            res.status(200).json({ staff: (_.first(result.rows) || {}) });
        }).catch(err => {
            res.customRespError(err);
        })
    },

    // get /api/club/event/:event/staff/:staff/primary-teams
    findStaffPrimaryTeams: function (req, res) {
        let $id                 = parseInt(req.params.staff, 10),
            $event_id           = parseInt(req.params.event, 10),
            $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10),
            season              = sails.config.sw_season.current;
        
        if(!$id)
            return res.validation('Invalid athlete identifier passed');
        if(!$event_id) 
            return res.validation('Invalid event identifier passed');
        if(!$master_club_id) 
            return res.forbidden('No club found. Forbidden');

        Db.query(
            `SELECT 
                "roles_data".*, COALESCE(
                    (
                        SELECT "d".mt FROM (
                            SELECT mt, count(mt) OVER()
                            FROM JSON_ARRAY_ELEMENTS("roles_data".master_teams) "mt"
                            WHERE (mt->>'primary')::BOOLEAN IS TRUE
                        ) "d"
                        WHERE "d".count = 1
                ), '{}'::JSON) "primary_master_team", COALESCE(
                    (
                        SELECT "d".rt from (
                            SELECT rt, count(rt) OVER()::INT
                            FROM JSON_ARRAY_ELEMENTS("roles_data".roster_teams) "rt"
                            WHERE (rt->>'primary')::BOOLEAN IS TRUE
                        ) "d"
                        WHERE "d".count = 1
                ), '{}'::JSON) "primary_roster_team"
            FROM (
                SELECT (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("master_roles"))), '[]'::JSON)
                    FROM (
                        SELECT 
                            msr.master_staff_role_id, msr.primary, 
                            mt.team_name "name", mt.organization_code "usav_code", mt.master_team_id,
                            r.name "role_name"
                        FROM "master_staff_role" msr
                        LEFT JOIN "role" r 
                            ON r.role_id = msr.role_id
                        INNER JOIN "master_team" mt 
                            ON mt.master_team_id = msr.master_team_id
                            AND mt.season = ms.season
                            AND mt.deleted IS NULL
                        WHERE msr.master_staff_id = ms.master_staff_id
                    ) "master_roles"
                ) "master_teams", (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("roster_roles"))), '[]'::JSON)
                    FROM (
                        SELECT 
                            rsr.roster_staff_role_id, rsr.primary,
                            rt.team_name "name", rt.organization_code "usav_code", rt.roster_team_id, 
                                                                                        rt.master_team_id,
                            COALESCE(r.name, mr.name) "role_name", COALESCE(rsr.primary, msr.primary) "result_primary",
                            ( (rsr.deleted IS NOT NULL OR rsr.deleted_by_user IS NOT NULL) ) "removed"
                        FROM "roster_staff_role" rsr 
                        LEFT JOIN "master_staff_role" msr 
                            ON msr.master_staff_id = rsr.master_staff_id
                            AND msr.master_team_id = rsr.master_team_id
                        LEFT JOIN "role" r 
                            ON r.role_id = rsr.role_id
                        LEFT JOIN "role" mr 
                            ON mr.role_id = msr.role_id
                        INNER JOIN "roster_team" rt 
                            ON rt.roster_team_id = rsr.roster_team_id
                            AND rt.event_id = $2
                            AND rt.deleted IS NULL
                        WHERE rsr.master_staff_id = ms.master_staff_id
                            AND rsr.deleted IS NULL
                    ) "roster_roles"
                ) "roster_teams"
                FROM "master_staff" ms
                INNER JOIN "roster_staff_role" rsr 
                    ON rsr.roster_staff_role_id = $1
                    AND rsr.master_staff_id = ms.master_staff_id
                WHERE ms.master_club_id = $3
                    AND ms.season = $4
            ) "roles_data"`,
        [$id, $event_id, $master_club_id, season]
        ).then(function  (result) {
            res.status(200).json({ staffer: _.first(result.rows) || {} });
        }).catch(function (err) {
            res.customRespError(err);
        });
    },

    // post /api/club/event/:event/staff/:staff/withdraw
    withdraw: function (req, res) {
        var $id = parseInt(req.params.staff, 10),
            $event_id = parseInt(req.params.event, 10),
            $master_club_id = parseInt(req.session.passport.user.master_club_id, 10);

        if(!$id) return res.status(400).json({ validation: 'Invalid athlete identifier passed' });
        if(!$event_id) return res.status(400).json({ validation: 'Invalid event identifier passed' });
        if(!$master_club_id) return res.status(403).json({ validation: 'No club found. Forbidden' });

        RosterSnapshotService.withdrawByUser($id, $master_club_id, $event_id, 'staff')
        .then(function () {
            res.ok()
        }).catch(function (err) {
            res.customRespError(err);
        });
    },
    // put /api/club/event/:event/staff/:staff/update
    update: function (req, res) {
        
        const $id              = parseInt(req.params.staff, 10),
            $event_id                   = Number(req.options.event || req.params.event),
            $master_club_id    = parseInt(req.session.passport.user.master_club_id, 10),
            $user_id           = parseInt(req.session.passport.user.user_id),
            /**
             * @type {Object} $defaultChanges
             * @type {Number} $defaultChanges.role_id
             * @type {Number} $defaultChanges.primary_team_id
             */
            $defaultChanges     = req.body.d,
            $override                    = req.body.o;

        if(!$id)
            return res.validation('Invalid athlete identifier passed');
        if(!$event_id)
            return res.validation('Invalid event identifier passed');
        if(!$master_club_id)
            return res.forbidden('No club found. Forbidden');
        if(_.isEmpty($defaultChanges) && _.isEmpty($override))
            return res.validation('Nothing to save: no data changed'); 

        const PRIVATE_LINK_REG = CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK;

        Db.query(
            `SELECT 
                rsr.roster_staff_role_id, msr.master_staff_role_id, rt.locked, rt.online_checkin_date, rt.roster_team_id,
                TO_CHAR(COALESCE(d.roster_deadline, e.roster_deadline), 'Mon DD, YYYY, HH12:MI AM') "deadline",
                (COALESCE(d.roster_deadline, e.roster_deadline) < (NOW() AT TIME ZONE e.timezone)) "deadline_passed",
                rsr.master_staff_id "staff_id", r.short_name "role_name", FORMAT('%s %s', ms.first, ms.last) "name",
                rsr.primary, rm.short_name "master_role"
             FROM "roster_staff_role" rsr
             LEFT JOIN "master_staff_role" msr
                ON msr.master_staff_id = rsr.master_staff_id
                AND msr.master_team_id = rsr.master_team_id
             INNER JOIN "roster_team" rt
                ON rt.roster_team_id = rsr.roster_team_id
                AND rt.event_id = $2
                AND rt.deleted IS NULL 
             INNER JOIN "event" e 
                ON e.event_id = rt.event_id
                AND (
                    CASE WHEN rt.reg_method = $4
                        THEN TRUE
                        ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                    END    
                )
                AND e.registration_method = 'club'  
                AND e.live_to_public IS TRUE 
                AND e.published IS TRUE 
                AND e.teams_use_clubs_module IS TRUE
             INNER JOIN "division" d 
                 ON d.division_id = rt.division_id 
             LEFT JOIN master_team mt  
                 ON mt.master_team_id = rt.master_team_id 
             INNER JOIN master_club mc  
                 ON mc.master_club_id = mt.master_club_id 
                 AND mc.master_club_id = $3
             LEFT JOIN role r
                 ON r.role_id = COALESCE(NULLIF(rsr.role_id, 0), msr.role_id)
             LEFT JOIN role rm
                 ON rm.role_id = msr.role_id
             LEFT JOIN master_staff ms
                 ON ms.master_staff_id = rsr.master_staff_id
             WHERE rsr.roster_staff_role_id = $1
                AND rsr.deleted IS NULL`,
            [$id, $event_id, $master_club_id, PRIVATE_LINK_REG]
        ).then(function (result) {
            var staff = _.first(result.rows);
            if(_.isEmpty(staff)) {
                throw {
                    validation: `Operation Failed: Staff not found has been removed 
                    or the Staff's Team has been removed`
                }
            }
            if (staff.locked) {
                throw {
                    validation: `Can't change roster information. Roster is locked. 
                    ${(!_.isEmpty($defaultChanges)?'To Update the default values, please, use "Staff Tab"':'')}`
                }
            }
            return staff;
        }).then(function (staff) {
            return Db.begin()
            .then(function (tr) {
                return Promise.all([
                    __updateStaffRow(
                        tr, $defaultChanges, staff.master_staff_role_id, $event_id, staff.staff_id, 'master_staff_role'
                    ),
                    __updateStaffRow(
                        tr, $override, staff.roster_staff_role_id, $event_id, staff.staff_id, 'roster_staff_role'
                    )
                ]).then(function (result) {
                    var defaultUpdates  = result[0],
                        overrideUpdates = result[1]

                    let updatedData = {
                        d: {
                            role_name: defaultUpdates && defaultUpdates.role_name
                        },
                        o: {
                            role_name: overrideUpdates && overrideUpdates.role_name
                        }
                    };
                    if(_.isNull(updatedData.o.role_name)) {
                        updatedData.o.role_name = staff.master_role;
                    }
                    
                    return updatedData;
                }).then((updatedData) => {
                    
                    const result = {
                        data    : updatedData,
                        tr      : tr
                    };
                    
                    const oldData = {
                        name: staff.name,
                        roleName: _.has($override, 'role_id') ? staff.role_name
                            : _.has($defaultChanges, 'role_id') ? staff.master_role : undefined,
                        primary: _.has($override, 'primary_team_id') ? (staff.primary ? 'enable' : 'disable') : undefined
                    }
                    const newData = {
                        name: staff.name,
                        roleName: _.has($override, 'role_id') ? updatedData.o.role_name
                            : _.has($defaultChanges, 'role_id') ? updatedData.d.role_name : undefined,
                        primary: _.has($override, 'primary_team_id') ? ($override.primary_team_id ? 'enable' : 'disable') : undefined
                    }
                    
                    if(!_.isEqual(oldData, newData)) {
                        const query= knex('event_change').insert({
                            event_id: $event_id,
                            roster_team_id: staff.roster_team_id,
                            user_id: $user_id,
                            action: 'team.roster.staff.changed.eo',
                            old_data: JSON.stringify(oldData),
                            new_data: JSON.stringify(newData)
                        })
                        return tr.query(query).then(() => {
                            return result;
                        });
                        
                    } else {
                        return result;
                    }
                }).catch(function (err) {
                    if(tr && !tr.isCommited) {
                        tr.rollback()
                    }
                    throw err;
                })
            }).then(function (data) {
                return data.tr.commit().then(function () {
                    return data.data;
                })
            })
        }).then(function (respBody) {
            res.status(200).json(respBody);
        }, function (err) {
            res.customRespError(err);
        })
    },
    /**
    * Create a role without master_staff_role??
    */
    create: function () {

    },
    // post /api/v2/club/staff/events_list
    findAssignedEvents: function (req, res) {
        var $staff = req.body.staff,
            $master_club_id = parseInt(req.session.passport.user.master_club_id, 10);

        if(!_.isArray($staff))
            return res.validation('Expecting staff to be an array of integers');
        if(!$staff.length)
            return res.validation('Expecting staff not to be empty');
        if(!$master_club_id)
            return res.validation('No club found. Access denied', 403);

        let staffNumArr = _.filter($staff, Number.isInteger);

        if(!staffNumArr.length)
            return res.validation('Expecting staff ids to be integers');

        let staffPGArr = `{${staffNumArr.join(',')}}`;

        const PRIVATE_LINK_REG = CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK;

        let FIND_ASSIGNED_EVENTS =
        `SELECT DISTINCT rt.event_id, e.name, e.long_name 
         FROM roster_staff_role rsr 
         INNER JOIN master_staff ms 
             ON ms.master_staff_id = rsr.master_Staff_id 
             AND ms.master_club_id = $1 
         INNER JOIN roster_team rt  
             ON rt.roster_team_id = rsr.roster_team_id  
             AND rt.deleted IS NULL  
             AND rt.status_entry IN (12, 13, 14)  
         LEFT JOIN "division" d  
             ON d.division_id = rt.division_id  
         INNER JOIN "event" e  
             ON e.event_id = rt.event_id  
             AND (
                CASE WHEN rt.reg_method = $2
                    THEN TRUE
                    ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                END    
             ) 
             AND COALESCE(d.roster_deadline, e.roster_deadline) >= (NOW() AT TIME ZONE e.timezone)  
             AND e.registration_method = 'club'  
             AND e.live_to_public IS TRUE  
             AND e.published IS TRUE 
             AND e.teams_use_clubs_module IS TRUE 
         WHERE rsr.master_staff_id = ANY($3)
             AND rsr.deleted IS NULL`;

        Db.query(
            FIND_ASSIGNED_EVENTS, 
            [$master_club_id, PRIVATE_LINK_REG, staffPGArr]
        ).then(result => {
            res.status(200).json({ events: result.rows });
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // post /api/club/event/:event/staff/:staff/reinstate
    reinstate: function (req, res) {
        var $id                 = parseInt(req.params.staff, 10),
            $event_id           = parseInt(req.params.event, 10),
            $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10);

        if(!$id) 
            return res.validation('Invalid athlete identifier passed');
        if(!$event_id) 
            return res.validation('Invalid event identifier passed');
        if(!$master_club_id) 
            return res.forbidden('No club found. Forbidden');

        RosterSnapshotService.reinstateMember($id, $master_club_id, $event_id, 'staff')
        .then(function () {
            res.ok()
        }).catch(function (err) {
            res.customRespError(err);
        });
    }
}

function __changeStafferRole (tr, roleId, id, eventId, table) {
    var sql = squel.update().table(table)
                .set('role_id', roleId)
                .where(`${table}_id = ?`, id)
                .returning(
                    `(
                        SELECT r.short_name FROM "role" r 
                        WHERE r.role_id = ${table}.role_id
                     ) "role_name", ${table}.*`
                );
    return tr.query(sql)
    .then(function (result) {
        return _.first(result.rows) || {};
    })
}

function __changeStafferPrimaryTeam (tr, primaryTeamId, id, eventId, masterStaffId, table) {
    var isRosterTable   = (table === 'roster_staff_role'),
        sql             = squel.update().table(table).where('master_staff_id = ?', masterStaffId).returning('*');

    if(isRosterTable) {
        sql.where(
            `EXISTS (
                SELECT * FROM "roster_team" rt
                INNER JOIN "event" e 
                    ON e.event_id = rt.event_id
                INNER JOIN "division" d 
                    ON d.division_id = rt.division_id 
                WHERE rt.roster_team_id = "${table}".roster_team_id 
                AND rt.event_id = ?
                AND COALESCE(d.roster_deadline, e.roster_deadline) > (NOW() AT TIME ZONE e.timezone)
            )`, eventId
        )
    }

    return Promise.all([
        tr.query(
            sql.clone().set('"primary" = TRUE')
            .where(`${(isRosterTable)?'roster':'master'}_team_id = ?`, primaryTeamId)
        ), 
        tr.query(
            sql.clone().set('"primary" = FALSE')
            .where(`${(isRosterTable)?'roster':'master'}_team_id <> ?`, primaryTeamId)
        )
    ])
}

// Set "primary" field value to FALSE or NULL
function __unsetPrimaryValue (tr, primaryValue, id, eventId, masterStaffId, table) {
    var isRosterTable = (table === 'roster_staff_role'),
        sql = squel.update().table(table)
                .set('"primary"', primaryValue)
                .where('master_staff_id = ?', masterStaffId)
                .returning('*');

    if(isRosterTable) {
        sql.where(
            `EXISTS (
                SELECT * FROM "roster_team" rt 
                WHERE rt.roster_team_id = "${table}".roster_team_id 
                AND rt.event_id = ?
            )`, eventId
        )
    }
    return tr.query(sql)
}

function __updateStaffRow (tr, data, id, eventId, staffId, table) {
    if(_.isEmpty(data))
        return Promise.resolve({});

    var updateStafferPromises = [];

    if(data.role_id !== undefined) {
        updateStafferPromises.push(
            __changeStafferRole(tr, data.role_id, id, eventId, table)
        )
    }
    if(data.primary_team_id !== undefined) {
        if(_.isNumber(data.primary_team_id)) {
            updateStafferPromises.push(
                __changeStafferPrimaryTeam(tr, data.primary_team_id, id, eventId, staffId, table)
            )
        } else {
            updateStafferPromises.push(
                __unsetPrimaryValue(tr, (data.primary_team_id === null)?null:false, id, eventId, staffId, table)
            )
        }
    }

    return Promise.all(updateStafferPromises).then(function (result) {
        return _.first(result);
    })
}
