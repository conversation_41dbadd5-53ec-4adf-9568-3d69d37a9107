'use strict';

const co = require('co');

var passport = require('passport'),
    crypto = require('crypto');

module.exports = {
    signin:  _signin,
    signout: function (req, res) {
        res.clearCookie('remember_me');
        res.clearCookie('sails.sid');

        if (req.user && req.user.user_id && req.sessionID) {
            RedisService.delUserDataMonitorKey(req.user.user_id, req.sessionID)
                .catch(ErrorSender.defaultError.bind(ErrorSender));

            UserService.history.saveLogoutAction({
                userID      : req.user.user_id,
                sessionID   : req.sessionID,
                ip          : req.getIP(),
                userAgent   : req.getUserAgent(),
            }).catch(err => loggers.errors_log.error(err));
        }

        req.logout(function (err) {
            if (err) {
                loggers.errors_log.error(err);
            }
            req.session.destroy(function (err) {
                if (err) {
                    loggers.errors_log.error(err);
                }
                res.ok();
            });
        });
    },

    facebook: function (req, res) {
        res[200](req.user);
    },

    twitter: function (req, res) {
        res[200](req.user);
    },

    google: function (req, res) {
        res[200](req.user);
    },
    // post /api/eo/login
    eo_signin: function (req, res) {
        var email = req.body.email,
            token = req.body.password;
        if(!email) return res.status(400).json({error: 'No email'});
        if(!token) return res.status(400).json({error: 'No token'});

        _signin (req, res);
    }
};

async function _signin (req, res) {

    try {
        const user = await auth(req, res);

        if (!user.activated) {
            throw {
                validationErrors: {
                    message: 'Your account is not activated',
                    path: 'email',
                },
                code: 400,
            };
        }

        if (user.deleted_at) {
            throw {
                validationErrors: {
                    message: 'This account has already been deleted',
                    path: 'email',
                },
                code: 400,
            };
        }

        await login(user);

        if (req.body.remember_me) {
            await setRememberMeToken(req, res, user);
        }

        if (!(
            user.clubDirector || user.eventOwner || user.sponsor ||
            user.staff || user.sales || user.owner || user.housing)
        ) {
            user.has_tickets = await findTickets(user);
        }

        res[200]({
            user: {
                country: user.country,
                first: user.first,
                last: user.last,
                clubDirector: user.role_club_director,
                eventOwner: user.role_event_owner || user.has_shared_events,
                hasEORole: user.role_event_owner,
                isUSAVAdmin: user.has_usav_admin_role,
                eoid: user.event_owner_id,
                sponsor: user.role_sponsor,
                staff: user.role_staff,
                sales: user.role_sales_manager,
                owner: user.is_sw_owner,
                housing: user.housing_company_id,
                has_tickets: user.has_tickets,
                hasGodRole: user.has_god_role,
                is_admin: (UserService.reg.ADMIN_EMAILS.indexOf(user.email.trim().toLowerCase()) !== -1),
                email: user.email,
                is_recognition_verified: user.is_recognition_verified,
                recognition_verification_status: user.recognition_verification_status,
                allow_login_as_cd: user.allow_login_as_cd,
            }
        });
    }
    catch(err) {
        if(err) {
            loggers.errors_log.error(err);
            if(err.code === 401) {
                return res.status(401).json({ error: err.error });
            } else if(err.code !== 500) {
                return res[err.code]
                            ?res.status(400).json({ validationErrors: [err.validationErrors] })
                            :res.serverError();
            }
        }
        return res.serverError();
    }

    function auth (req, res) {
        return new Promise((resolve, reject) => {
            passport.authenticate('user-local', (err, user, info) => {
                if (err) {
                    reject(err);
                } else if (_.isEmpty(user)) {
                    reject({ validationErrors: info, code: 400 });
                } else {
                    RedisService.setUserDataMonitorKey(user.user_id, req.sessionID)
                        .catch(ErrorSender.defaultError.bind(ErrorSender));
                    resolve(user);
                }
            })(req, res);
        });
    }

    function login (user) {
        return new Promise((resolve, reject) => {
            req.logIn(user, (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            })
        })
    }

    function setRememberMeToken (req, res, user) {
        let token = crypto.randomBytes(32).toString('hex');

        let sql =
            squel.update().table('user')
                .set('remember_me', token)
            .where('user_id = ?', user.user_id);

        return Db.query(sql)
        .then(() => {
            res.cookie(sails.config.sessionKeys.rememberKey, token, {
                path        : '/',
                httpOnly    : true,
                maxAge      : 30 * 24 * 60 * 60 * 1000
                // domain: __setDomain(req.host)
            });
        });
    }

    function findTickets (user) {
        return Db.query(
            `SELECT p.purchase_id 
             FROM "purchase" p 
             WHERE p.payment_for = 'tickets' 
                AND p.user_id = $1
             LIMIT 1`,
            [user.user_id]
        ).then(result => (result.rows.length > 0))
    }
}

function __setDomain (hostname) {
    if(!hostname) return;
    var splitted = hostname.split('.');
    if(splitted.length === 1) {
        return '.' + hostname;
    }
    splitted.shift();
    return '.' + splitted.join('.')

}

