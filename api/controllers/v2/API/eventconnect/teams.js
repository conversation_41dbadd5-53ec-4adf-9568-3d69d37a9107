module.exports = {

    friendlyName: 'Teams',
    description: 'Event Connect teams API endpoint',

    inputs: {
        event: {
            type: 'number',
            example: 22,
            description: 'Event ID',
            required: true
        },
        timestamp: {
            type: 'string',
            example: '2022-08-30T13:10:35.116Z',
            description: 'Current UTC timestamp',
            required: true
        },
        team: {
            type: 'number',
            example: 33,
            description: 'Team ID'
        }
    },

    exits: {
        success: {
            statusCode: 200
        },
        unauthorized: {
            statusCode: 401
        }
    },

    fn: async function(inputs, exits) {
        let housingCompanyName = 'Event Connect';
        let eventId = inputs.event;
        let timestamp = inputs.timestamp;
        let teamId = inputs.team;
        let now = new Date(); // Get current server time
        let serverTime = now.toJSON(); // Convert it date string in ISO 8601 format

        try {
            let query = `
                            SELECT
                                e.event_id,
                                e.name,
                                e.long_name,
                                to_char(e.date_start, \'YYYY-MM-DD HH24:MI:SS\') date_start,
                                to_char(e.date_end, \'YYYY-MM-DD HH24:MI:SS\') date_end,
                                e.city,
                                e.state,
                                e.housing_teams_access_level
                            FROM event e
                            INNER JOIN housing_company hc ON hc.housing_company_id = e.housing_company_id
                            WHERE e.published = TRUE
                            AND e.teams_use_clubs_module IS TRUE
                            AND e.live_to_public IS TRUE
                            AND e.date_end > (now() AT TIME ZONE e.timezone)
                            AND e.has_status_housing = TRUE
                            AND hc.name = $1
                            AND e.event_id = $2
                        `;
            let params = [housingCompanyName, eventId];
            let eventsResult = await Db.query(query, params);

            if (eventsResult.rows.length < 1) {
                let errMsg = 'Event ID not found';
                loggers.errors_log.error(errMsg);
                return this.res.status(400).json({ error: errMsg.toString() });

            }

            let eventRow = eventsResult.rows[0];
            let event = {
                'id': eventRow.event_id ?? '',
                'name': eventRow.long_name ?? '',
                'shortName': eventRow.name ?? '',
                'dateStart': eventRow.date_start ?? '',
                'dateEnd': eventRow.date_end ?? '',
                'city': eventRow.city ?? '',
                'state': eventRow.state ?? ''
            };
            let housingTeamsAccessLevel = eventRow.housing_teams_access_level;

            // remove first item from params O(N)
            params.shift();

            // Getting Teams Info
            // Note: when use ' (single quote) inside query will be error syntax error at or near "'some_column'".
            // Need use " (double quote)
            query = `
                        SELECT
                            t.roster_team_id, t.roster_club_id, t.team_name, d.name division_name, t.organization_code,
                            t.deleted, t.ths_tentative_nights, t.ths_confirmed_nights,
                            TO_CHAR(t.modified, 'YYYY-MM-DD HH24:MI:SS') modified, t.status_paid, t.status_entry,
                            tst."status_value" "acceptance",
                            (SELECT TRUE
                              FROM purchase_team AS pt
                                       JOIN purchase p
                                            ON p.purchase_id = pt.purchase_id
                                                AND p.type = 'ach'
                                                AND p.status = 'pending'
                              WHERE pt.roster_team_id = t.roster_team_id
                                AND pt.event_id = t.event_id
                                AND pt.canceled IS NULL)::BOOLEAN IS TRUE AS has_pending_ach_payment,
                            t."ths_trav_coord_name",
                            t."ths_trav_coord_email",
                            t."ths_trav_coord_phone"
                          FROM roster_team t
                          INNER JOIN "team_status" tst
                            ON tst."team_status_id" = t."status_entry"
                          LEFT JOIN division d
                            ON d.division_id = t.division_id
                          WHERE t.event_id = $1
             `;

            if (timestamp) {
                let localTsp = sails.helpers.housing.utcToLocal(timestamp);
                params.push(localTsp);
                query += ' AND t.modified > $' + params.length;
            }
            if (teamId) {
                params.push(teamId);
                query += ' AND t.roster_team_id = $' + params.length;
            }

            let teamsResult = await Db.query(query, params);

            let teams = [];
            for (const row of teamsResult.rows) {
                let allowBooking = sails.helpers.housing.isBookingAllowed.with({
                    accessLevel: housingTeamsAccessLevel,
                    teamDeleted: row.deleted,
                    teamPaidStatus: row.status_paid,
                    teamEntryStatus: row.status_entry,
                    hasPendingAchPayment: row.has_pending_ach_payment,
                });

                teams.push({
                    id: row.roster_team_id ?? '',
                    clubid: row.roster_club_id ?? '',
                    name: row.team_name ?? '',
                    division: row.division_name ?? '',
                    teamCode: row.organization_code ?? '',
                    allowBooking: allowBooking,
                    tentativeNights: row.ths_tentative_nights ?? '',
                    confirmedNights: row.ths_confirmed_nights ?? '',
                    modified: row.modified ?? '',
                    acceptance: row.acceptance ?? '',
                    travCoordName: row.ths_trav_coord_name ?? '',
                    travCoordEmail: row.ths_trav_coord_email ?? '',
                    travCoordPhone: row.ths_trav_coord_phone ?? ''
                });

            }

            exits.success({
                'serverTime': serverTime,
                'event': event,
                'teams': teams
            });

        } catch(err) {
            loggers.errors_log.error(err);
            this.res.status(500).json({ error: err.toString() });

        }
    }
};
