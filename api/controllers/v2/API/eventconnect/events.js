module.exports = {

    friendlyName: 'Events',
    description: 'Event Connect events API endpoint',

    inputs: {},

    exits: {
        success: {
            statusCode: 200
        },
        unauthorized: {
            statusCode: 401
        }
    },

    fn: async function(inputs, exits) {
        let housingCompanyName = 'Event Connect';
        let now = new Date(); // Get current server time
        let serverTime = now.toJSON(); // Convert it date string in ISO 8601 format

        let query = `
                    SELECT e.event_id, e.name, e.long_name,
                    to_char(e.date_start, 'YYYY-MM-DD HH24:MI:SS') date_start,
                    to_char(e.date_end, 'YYYY-MM-DD HH24:MI:SS') date_end,
                    e.city,
                    e.state
                    FROM event e
                    INNER JOIN housing_company hc ON hc.housing_company_id = e.housing_company_id
                    WHERE e.published = TRUE
                    AND e.teams_use_clubs_module IS TRUE
                    AND e.live_to_public IS TRUE
                    AND e.date_end > (now() AT TIME ZONE e.timezone)
                    AND e.has_status_housing = TRUE
                    AND hc.name = $1
            `;

        try {
            let events = [];
            let eventsResult = await Db.query(query, [housingCompanyName]);

            for (const row of eventsResult.rows) {
                events.push({
                    'id': row.event_id ?? '',
                    'name': row.long_name ?? '',
                    'shortName': row.name ?? '',
                    'dateStart': row.date_start ?? '',
                    'dateEnd': row.date_end ?? '',
                    'city': row.city ?? '',
                    'state': row.state ?? ''
                });
            }

            exits.success({
                serverTime: serverTime,
                events: events
            });
        } catch(err) {
            loggers.errors_log.error(err);
            this.res.status(500).json({ error: err.toString() });
        }
    }
};
