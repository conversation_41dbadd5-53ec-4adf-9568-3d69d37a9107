const { FORM_TYPE: EVENT_CUSTOM_FORM_TYPE  } = require('../constants/event-custom-form');
const { PAYMENT_OPTION } = require('../constants/event-official');
const { CUSTOM_FIELD_TYPE } = require('../constants/sales-hub');
const { PAYMENT_METHOD } = require('../constants/payments');

exports.REGEX_MESSAGE =  {
    messages: {
        'string.regex': {
            base: 'Is Invalid'
        }
    }
};

exports.ZIP_MESSAGE = {
    messages: {
        'string.regex': {
            base: 	'!!This zip code’s format is invalid for the country you have selected',
            name:	'!!This zip code’s format is invalid for the country you have selected'
        }
    }
};
exports.CA_ZIP_REG_EXP = /^(?!.*[DFIOQU])[A-VXY][0-9][A-Z] [0-9][A-Z][0-9]$/i;
exports.AAU_CA_ZIP_REG_EXP = /^(?!.*[DFIOQU])[A-VXY][0-9][A-Z]( )?[0-9][A-Z][0-9]$/i;
exports.BM_ZIP_REG_EXP = /^[A-Za-z]{2}\s([A-Za-z]{2}|\d{2})$/;
exports.US_ZIP_REG_EXP = /^(\d{5}(\d{4})?)?$/;
exports.CH_ZIP_REG_EXP = /^[1-9][0-9]{5}$/;
exports.CO_ZIP_REG_EXP = /^[0-9]{6}$/;
exports.HN_ZIP_REG_EXP = /^[0-9]{5}$/;
exports.GENDER_VALUES = ['male', 'female', 'coed'];
exports.NAME_REGEX = /^[A-Za-z.\-'’ ]+$/i;
exports.EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
exports.PHONE_REGEX = /^([0-9]{10,15})$/;
exports.TICKET_TYPES = {
    DAILY: 'daily',
    WEEKEND: 'weekend',
    OTHER: 'other',
};
exports.USAV_REG_EXP = /^([A-Z]{2}\d{7}[A-Z]{2,3}\d{2,3}|\d{7})$/i;
exports.TEAM_ENTRY_STATUS = {
    DECLINED: 11,
    ACCEPTED: 12,
    PENDING: 13,
    WAIT_LIST: 14,
};
exports.TEAM_PAYMENT_STATUS = {
    PAID: 22,
    NONE: 21,
    PARTIAL: 23,
    PENDING: 24,
    REFUNDED: 25,
    DISPUTED: 26
}
exports.TEAM_USAV_SEASONALITY = {
    FULL: 'full',
    LOCAL: 'local',
}
exports.SW_FEE_PAYER = {
    BUYER: 'buyer',
    SELLER: 'seller'
}
exports.TEAMS_SW_FEE_COLLECTION_MODE = {
    AUTO: 'auto',
    MANUAL: 'manual',
    AUTO_CARD: 'auto_card'
}
exports.EVENT_CUSTOM_FORM_TYPE = EVENT_CUSTOM_FORM_TYPE;
exports.OFFICIALS_PAYMENT_OPTION = PAYMENT_OPTION;
exports.SALES_HUB = {
    CUSTOM_FIELD_TYPE: CUSTOM_FIELD_TYPE
};
exports.PAYMENT_METHOD = PAYMENT_METHOD;

