- hosts: all
  vars:
      deploy_folder: /home/<USER>/sw-main
      image_name: sw-main
      container_name: sw-main
      schedule_container_name: scheduler-worker-dev
      workers_queue_container_name: workers-queue-dev
      sw_main1_container_name: sw-main1
      sw_main2_container_name: sw-main2

  tasks:
  - name: get the non root remote user
    shell: id -u
    register: user_id

  - name: Check if schedule container is exists
    shell: docker container inspect "{{ schedule_container_name }}"
    register: schedule_container_exists
    ignore_errors: True

  - name: Stop scheduler-worker-dev
    shell: docker stop "{{ schedule_container_name }}"
    when: schedule_container_exists is succeeded

  - name: Remove scheduler-worker-dev
    shell: docker rm "{{ schedule_container_name }}"
    when: schedule_container_exists is succeeded

  - name: Start scheduler-worker-dev
    shell: >
      docker run -d --restart=always --memory=1500M -u `id -u $USER`:`id -g $USER` --env HOME=.
      --name "{{ schedule_container_name }}"
      -w /home/<USER>/app
      -v "$PWD":/home/<USER>/app
      -v "$PWD"/../logs:/home/<USER>/logs
      -v "$PWD"/../connection:/home/<USER>/connection
      -v "$PWD"/../sw-config:/home/<USER>/sw-config
      -v "$PWD"/../passbook_keys:/home/<USER>/passbook_keys
      -e LOG_PG_CS="{{ LOG_PG_CS }}"
      -e LOG_APP_ID="{{ LOG_APP_ID }}"
      -e NODE_ENV="{{ NODE_ENV }}"
      -e REDIS_URL="{{ REDIS_URL }}"
      -e SW_DB="{{ SW_DB }}"
      -e WORK_DIR="/home/<USER>/sw-main"
      node:16 node scheduler/scheduler.worker.js --dev
    args:
      chdir: "{{ deploy_folder }}"

  - name: Check if workers-queue-dev container is exists
    shell: docker container inspect "{{ workers_queue_container_name }}"
    register: workers_queue_container_exists
    ignore_errors: True

  - name: Stop workers-queue-dev
    shell: docker stop "{{ workers_queue_container_name }}"
    when: workers_queue_container_exists is succeeded

  - name: Remove workers-queue-dev
    shell: docker rm "{{ workers_queue_container_name }}"
    when: workers_queue_container_exists is succeeded

  - name: Start workers-queue-dev
    shell: >
      docker run -d --restart=always --memory=1500M -u `id -u $USER`:`id -g $USER` --env HOME=.
      --name "{{ workers_queue_container_name }}"
      -w /home/<USER>/app
      -v "$PWD":/home/<USER>/app
      -v "$PWD"/../logs:/home/<USER>/logs
      -v "$PWD"/../connection:/home/<USER>/connection
      -v "$PWD"/../sw-config:/home/<USER>/sw-config
      -v "$PWD"/../passbook_keys:/home/<USER>/passbook_keys
      -e LOG_PG_CS="{{ LOG_PG_CS }}"
      -e LOG_APP_ID="{{ LOG_APP_ID }}"
      -e NODE_ENV="{{ NODE_ENV }}"
      -e REDIS_URL="{{ REDIS_URL }}"
      -e SW_DB="{{ SW_DB }}"
      -e WORK_DIR="/home/<USER>/sw-main"
      node:16 node worker/main.js --dev
    args:
      chdir: "{{ deploy_folder }}"

  - name: Tell haproxy to disable sw-main1
    shell: echo "set weight webservers/sw-main1 0" | socat stdio tcp4-connect:127.0.0.1:9999

  - name: Wait few seconds so sw-main1 can finish processing requests
    pause:
      seconds: 5

  - name: Check if sw-main1 container is exists
    command: docker container inspect "{{ sw_main1_container_name }}"
    register: sw_main1_container_exists
    ignore_errors: True

  - name: Stop sw-main1
    shell: docker stop "{{ sw_main1_container_name }}"
    when: sw_main1_container_exists is succeeded

  - name: Remove sw-main1
    shell: docker rm "{{ sw_main1_container_name }}"
    when: sw_main1_container_exists is succeeded

  - name: Start sw-main1
    shell: >
      docker run -d --restart=always --memory=1500M -u `id -u $USER`:`id -g $USER` --env HOME=.
      --name "{{ sw_main1_container_name }}"
      -p 3001:3001
      -w /home/<USER>/sw-main
      -v /home/<USER>/:/home/<USER>
      -e LOG_PG_CS="{{ LOG_PG_CS }}"
      -e LOG_APP_ID="{{ LOG_APP_ID }}"
      -e NODE_ENV="{{ NODE_ENV }}"
      -e REDIS_URL="{{ REDIS_URL }}"
      -e SW_DB="{{ SW_DB }}"
      -e WORK_DIR="/home/<USER>/sw-main"
      node:16 node app.js --dev
    args:
      chdir: "{{ deploy_folder }}"

  - name: Tell haproxy to enable sw-main1
    shell: echo "set weight webservers/sw-main1 1" | socat stdio tcp4-connect:127.0.0.1:9999

  - name: Wait few seconds so sw-main1 can start
    pause:
      seconds: 5

  - name: Tell haproxy to disable sw-main2
    shell: echo "set weight webservers/sw-main2 0" | socat stdio tcp4-connect:127.0.0.1:9999

  - name: Wait few seconds so sw-main2 can finish processing requests
    pause:
      seconds: 5

  - name: Check if sw-main2 container is exists
    command: docker container inspect "{{ sw_main2_container_name }}"
    register: sw_main2_container_exists
    ignore_errors: True

  - name: Stop sw-main2
    shell: docker stop "{{ sw_main2_container_name }}"
    when: sw_main2_container_exists is succeeded

  - name: Remove sw-main2
    shell: docker rm "{{ sw_main2_container_name }}"
    when: sw_main2_container_exists is succeeded

  - name: Start sw-main2
    shell: >
      docker run -d --restart=always --memory=1500M -u `id -u $USER`:`id -g $USER` --env HOME=.
      --name "{{ sw_main2_container_name }}"
      -p 3002:3001
      -w /home/<USER>/sw-main
      -v /home/<USER>/:/home/<USER>
      -e LOG_PG_CS="{{ LOG_PG_CS }}"
      -e LOG_APP_ID="{{ LOG_APP_ID }}"
      -e NODE_ENV="{{ NODE_ENV }}"
      -e REDIS_URL="{{ REDIS_URL }}"
      -e SW_DB="{{ SW_DB }}"
      -e WORK_DIR="/home/<USER>/sw-main"
      node:16 node app.js --dev
    args:
      chdir: "{{ deploy_folder }}"

  - name: Tell haproxy to enable sw-main2
    shell: echo "set weight webservers/sw-main2 1" | socat stdio tcp4-connect:127.0.0.1:9999
