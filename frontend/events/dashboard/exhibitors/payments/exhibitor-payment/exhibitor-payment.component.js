class ExhibitorPaymentComponent {
    constructor(ExhibitorsService, ExhibitorsPaymentsService, ConfirmationService, toastr, APPLICATION_STATUS) {
        this.ExhibitorsService = ExhibitorsService;
        this.ExhibitorsPaymentsService = ExhibitorsPaymentsService;
        this.ConfirmationService = ConfirmationService;
        this.toastr = toastr;

        this.exhibitorPaymentData = {};
        this.initExhibitorPaymentData = {};
        this.total = null;
        this.initTotal = null;
        this.saveInProcess = false;
        this.isFormSubmitted = false;

        this.PAYMENT_FIELDS = [
            'event_dates',
            'comment',
        ];
        this.APPLICATION_STATUS = APPLICATION_STATUS;
    }

    get isCreateMode() {
        return !this.exhibitorId;
    }

    get isUpdateMode() {
        return !!this.exhibitorId;
    }

    get modalTitle() {
        return this.isUpdateMode ? `Invoice for ${this.companyName}` : 'Create Invoice';
    }

    get savePaymentButtonText() {
        return this.isUpdateMode ? `Update` : 'Create';
    }

    get formattedExhibitorPaymentData() {
        const data = _.pick(this.exhibitorPaymentData, this.PAYMENT_FIELDS);

        data.booths = this.exhibitorPaymentData.selectedBooths.map(({ id }) => id);
        data.otherBooths = this.ExhibitorsService.generateBoothsForRequest(this.exhibitorPaymentData);
        data.amount = this.total;

        return data;
    }

    async savePayment() {
        if (this.saveInProcess) {
            return;
        }

        this.saveInProcess = true;
        this.isFormSubmitted = true;

        try {
            this.validateExhibitorPaymentData();

            const exhibitorPaymentData = this.formattedExhibitorPaymentData;

            const invoiceProcessed = await this.processExhibitorInvoice(exhibitorPaymentData);

            if(invoiceProcessed) {
                this.toastr.success('Exhibitor invoice successfully saved');
                if (this.isCreateMode) {
                    this.onClose({ withReload: true });
                }
            }
        } catch (error) {
            if (error.validation) {
                this.toastr.error(error.validation);
            }
        } finally {
            this.saveInProcess = false;
        }
    }

    validateExhibitorPaymentData() {
        const exhibitorPaymentData = this.exhibitorPaymentData;

        if (_.isEmpty(exhibitorPaymentData)) {
            return;
        }

        if(this.isUpdateMode && !this.isPendingPayment()) {
            throw {validation: 'You can change only pending payments'};
        }

        this.ExhibitorsService.validateExhibitorPaymentData(exhibitorPaymentData);
    }

    async processExhibitorInvoice(exhibitorPaymentData) {
        if (this.isCreateMode) {
            return this.createEventExhibitorInvoice(exhibitorPaymentData);
        }

        if (this.isRegenerateInvoiceMode()) {
            const regenerationConfirmed = await this.confirmInvoiceRegeneration();

            if (!regenerationConfirmed) {
                return false;
            }
        }

        return this.updateEventExhibitorInvoice(exhibitorPaymentData);
    }

    createEventExhibitorInvoice(exhibitorPaymentData) {
        const exhibitorId = this.exhibitorPaymentData.exhibitor_id;
        const paymentData = _.omit(exhibitorPaymentData, 'exhibitor_id');

        return this.ExhibitorsPaymentsService.createEventExhibitorInvoice(
            this.eventId,
            exhibitorId,
            paymentData
        );
    }

    isRegenerateInvoiceMode() {
        return this.ExhibitorsService.isRegenerateInvoiceMode(
            this.exhibitorPaymentData,
            this.initExhibitorPaymentData
        );
    }

    async confirmInvoiceRegeneration() {
        const answer = await this.ConfirmationService.ask(
            'The booths list has changed. Invoice will be re-generated.',
            { disableNoBtn: true }
        );
        return answer === this.ConfirmationService.YES_RESP;
    }

    updateEventExhibitorInvoice(exhibitorPaymentData) {
        return this.ExhibitorsPaymentsService.updateEventExhibitorInvoice(
            this.eventId,
            this.exhibitorId,
            this.eventExhibitorInvoiceId,
            exhibitorPaymentData
        )
    }

    showSaveButton() {
        if(this.isUpdateMode) {
            return this.isPendingPayment();
        }

        return true;
    }

    isPendingPayment() {
        return this.ExhibitorsPaymentsService.isPendingPayment(this.exhibitorPaymentData);
    }

    isApplicationApproved() {
        return this.exhibitorPaymentData.application_status === this.APPLICATION_STATUS.APPROVED;
    }
}

angular.module('SportWrench').component('exhibitorPayment', {
    templateUrl: 'events/dashboard/exhibitors/payments/exhibitor-payment/exhibitor-payment.html',
    bindings: {
        eventId: '<',
        exhibitorId: '<',
        eventExhibitorInvoiceId: '<',
        companyName: '<',
        onClose: '&',
        isFormSubmitted: '<',
    },
    controller: [
        'ExhibitorsService',
        'ExhibitorsPaymentsService',
        'ConfirmationService',
        'toastr',
        'APPLICATION_STATUS',
        ExhibitorPaymentComponent
    ]
});
