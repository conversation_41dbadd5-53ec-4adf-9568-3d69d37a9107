<div class="row">
    <div class="col-sm-12">
        <spinner active="$ctrl.loading.inProcess"></spinner>
        <uib-alert type="danger text-center" ng-if="$ctrl.loading.error">{{$ctrl.loading.error}}</uib-alert>
    </div>
    <div ng-if="!$ctrl.loading.inProcess && !$ctrl.loading.error" class="col-sm-12">
        <form class="exhibitor-event_registration-form">
            <div class="row exhibitor-apply-form-application_row" ng-if="$ctrl.isCreateMode">
                <div class="col-sm-3 text-right"><span class="text-bold">Company Name:</span></div>
                <div class="col-sm-9">
                    <div class="form-inline">
                        <div class="row">
                            <div class="col-sm-8">
                                <select
                                    ng-model="$ctrl.exhibitorId"
                                    class="form-control exhibitor-event_registration-form_booth-select"
                                    ng-options="exhibitor.sponsor_id as exhibitor.company_name for exhibitor in $ctrl.exhibitors"
                                    ng-change="$ctrl.onExhibitorChange()"
                                    required>
                                    <option value="" ng-if="!$ctrl.exhibitorId">Select...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" ng-if="$ctrl.exhibitorId && !$ctrl.isApplicationApproved() && $ctrl.isCreateMode">
                <div class="col-sm-offset-3 col-sm-9">
                    <div class="alert alert-warning">
                        Application status is not "Approved".
                    </div>
                </div>
            </div>
            <div class="row exhibitor-apply-form-application_row validation-required" ng-if="$ctrl.exhibitorId">
                <label class="col-sm-3 control-label text-right">
                    Dates:
                </label>
                <div class="col-sm-9">
                    <div class="row">
                        <div ng-repeat="(key, value) in $ctrl.exhibitorPaymentData.event_dates" class="col-sm-6">
                            <div class="exhibitor-event_registration-form_dates-item">
                                <label class="checkbox-inline">
                                    <input
                                        ng-model="$ctrl.exhibitorPaymentData.event_dates[key]"
                                        type="checkbox"
                                        ng-disabled="($ctrl.isUpdateMode && !$ctrl.isPaymentEditable()) || !$ctrl.isApplicationApproved()">
                                    <span>{{key | UTCdate: $ctrl.EVENT_DATES_FORMAT}}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <span class="text-danger" ng-if="$ctrl.isEventDatesEmpty() && $ctrl.isFormSubmitted">
                        Please select at least one date
                    </span>
                </div>
            </div>
            <div class="row exhibitor-apply-form-application_row validation-required" ng-if="$ctrl.exhibitorId">
                <label class="col-sm-3 control-label text-right">
                    Booth:
                </label>
                <div class="col-sm-9">
                    <div class="form-inline">
                        <div class="row">
                            <div class="col-sm-8">
                                <select
                                    ng-model="$ctrl.selectedBooth"
                                    class="form-control exhibitor-event_registration-form_booth-select"
                                    ng-options="booth as booth.label for booth in $ctrl.exhibitorPaymentData.event_booths"
                                    ng-disabled="($ctrl.isUpdateMode && !$ctrl.isPaymentEditable()) || !$ctrl.isApplicationApproved()">
                                    <option value="" selected>Choose a Booth...</option>
                                </select>
                                <div class="exhibitor-event_registration-form_selected-booths" ng-if="$ctrl.selectedBooths.length">
                                    <div class="exhibitor-event_registration-form_selected-booths_item" ng-repeat="booth in $ctrl.selectedBooths track by $index">
                                        <span>{{booth.label}}</span>
                                        <span
                                            class="pointer text-danger"
                                            ng-click="$ctrl.removeBooth($index)"
                                            ng-if="$ctrl.isCreateMode || $ctrl.isPaymentEditable()">
                                            <i class="fa fa-remove"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="row" ng-if="$ctrl.hasOtherBoothsWithoutFee()">
                                    <div class="col-sm-12">
                                        <span class="text-danger">Please add fee to all other booths</span>
                                    </div>
                                </div>
                                <div class="row" ng-if="$ctrl.hasOtherBoothsWithoutDescription()">
                                    <div class="col-sm-12">
                                        <span class="text-danger">Please add description to all other booths</span>
                                    </div>
                                </div>
                                <div class="row" ng-if="$ctrl.isBoothsEmpty() && !$ctrl.hasOtherBoothsWithoutFee() && $ctrl.isFormSubmitted">
                                    <div class="col-sm-12">
                                        <span class="text-danger">Please add at least one booth</span>
                                    </div>
                                </div>
                            </div>
                            <div ng-if="($ctrl.isCreateMode || $ctrl.isPaymentEditable()) && $ctrl.exhibitorPaymentData.event_booths.length" class="col-sm-4">
                                <button ng-click="$ctrl.addBooth()" class="btn btn-primary to-right" ng-disabled="!$ctrl.isApplicationApproved()">Add Booth</button>
                            </div>
                        </div>
                        <div ng-if="$ctrl.otherBooths.length" class="row">
                            <div class="col-sm-12">
                                <div class="row">
                                    <div ng-repeat="booth in $ctrl.otherBooths track by $index">
                                        <other-booth-form info="booth"></other-booth-form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row exhibitor-apply-form-application_row" ng-if="$ctrl.exhibitorId">
                <div class="col-sm-3 text-right"><span class="text-bold">Comment:</span></div>
                <div class="col-sm-6">
                    <textarea
                        ng-model="$ctrl.exhibitorPaymentData.comment"
                        class="full-width form-control"
                        rows="1"
                        ng-disabled="($ctrl.isUpdateMode && !$ctrl.isPaymentEditable()) || !$ctrl.isApplicationApproved()">
                    </textarea>
                </div>
            </div>
            <div ng-if="$ctrl.getBoothsTotal()" class="row exhibitor-apply-form-application_row">
                <div class="col-sm-3 text-right">
                    <span class="text-bold">Total:</span>
                </div>
                <div class="col-sm-9">
                    <span class="text-bold">{{$ctrl.getBoothsTotal() | currency}}</span>
                </div>
            </div>
            <div ng-show="$ctrl.isUpdateMode" class="row exhibitor-event_registration-form_payment-row">
                <div class="col-sm-12">
                    <payment-operations
                        type="$ctrl.exhibitorPaymentData.purchase_type"
                        status="$ctrl.exhibitorPaymentData.purchase_status"
                        check-number="$ctrl.exhibitorPaymentData.check_num"
                        purchase-id="$ctrl.exhibitorPaymentData.purchase_id"
                        date-paid="$ctrl.exhibitorPaymentData.date_paid"
                        on-modal-close="$ctrl.onModalClose({ withReload: true })"
                        event-id="$ctrl.eventId">
                    </payment-operations>
                </div>
            </div>
            <div ng-if="$ctrl.showPaymentComponent()" class="row exhibitor-event_registration-form_payment-row">
                <fieldset ng-disabled="$ctrl.in_progress">
                    <div class="col-sm-12">
                        <button ng-if="$ctrl.showPaymentButtons && !$ctrl.showPaymentChangeButton"
                                class="btn btn-primary"
                                ng-click="$ctrl.showCard()">
                            Pay by Credit Card
                        </button>
                        <button ng-if="$ctrl.showPaymentChangeButton"
                                class="btn btn-primary"
                                ng-click="$ctrl.showPaymentChange()">
                            Pay by Credit Card
                        </button>
                        <button ng-if="$ctrl.isPaymentEditable()"
                                class="btn btn-danger pull-right"
                                ng-click="$ctrl.deleteExhibitorPayment()">
                            Delete Invoice
                        </button>
                        <div class="row row-space" ng-show="$ctrl.showCardMenu()">
                            <div class="col-sm-12">
                                <stripe-elements-form
                                    ng-if="$ctrl.payment && $ctrl.show_card_menu"
                                    payment="$ctrl.payment"
                                    pay="$ctrl.payByCard(token)">
                                </stripe-elements-form>
                            </div>
                        </div>
                        <div class="row row-space" ng-show="$ctrl.in_progress">
                            <div class="col-sm-6">
                                <uib-progressbar
                                    class="progress-striped"
                                    value="$ctrl.progress_bar_value"
                                    type="success">
                                    {{$ctrl.progress_bar_value | number:0 }}%
                                </uib-progressbar>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </div>
        </form>
    </div>
</div>
