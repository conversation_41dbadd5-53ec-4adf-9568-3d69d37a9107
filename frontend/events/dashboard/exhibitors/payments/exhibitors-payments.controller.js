angular.module('SportWrench')
    .controller('events.ExhibitorsPaymentsController',
        function ($scope, $stateParams, ExhibitorsPaymentsService, toastr) {
            $scope.showStripeStatistic = $stateParams.showStripeStatistic;

            $scope.data = {
                payments: []
            };

            $scope.utils = {
                search: '',
                payments: [],
                filters: $stateParams.filters,
            };

            $scope.filterSearch = function () {
                if ($scope.utils.search === '') {
                    $scope.utils.payments = $scope.data.payments;
                } else {
                    $scope.utils.payments = $scope.data.payments.filter(payment => {
                        const searchTerm = $scope.utils.search.toUpperCase();
                        const companyNameMatch = payment.company_name.toUpperCase().includes(searchTerm);
                        const amountMatch = payment.amount.toString().toUpperCase().includes(searchTerm);

                        return companyNameMatch || amountMatch;
                    });
                }
            }

            $scope.numberOfUniqueCompanies = function () {
                return _.unique($scope.utils.payments.map(payment => payment.company_name)).length;
            }

            $scope.openCreateInvoiceModal = function () {
                const payment = {
                    eventId: $stateParams.event
                }

                if($scope.$parent && $scope.$parent.event&& $scope.$parent.event.exhibitors_sw_fee && Number($scope.$parent.event.exhibitors_sw_fee) === 0) {
                    toastr.warning('SW Fee not set, ask Event Supervisor for help');
                    return;
                }
                ExhibitorsPaymentsService.openExhibitorPaymentModal(payment)
                    .then((isSaved) => {
                        if (isSaved) {
                            $scope.loadExhibitorInvoices();
                        }
                    })
            }

            $scope.showClearFiltersButton = function () {
                return !_.isEmpty($scope.utils.filters) || $scope.utils.search;
            }

            $scope.clearFilters = function () {
                $scope.utils.search = null;
                $scope.utils.filters = {};

                $scope.loadExhibitorInvoices();
            }

            $scope.loadExhibitorInvoices = function () {
                ExhibitorsPaymentsService.getExhibitorInvoices($stateParams.event, $scope.utils.filters)
                    .then(function ({data: invoices}) {
                        $scope.data.payments = invoices;
                        $scope.utils.payments = invoices;
                    });
            }

            $scope.loadExhibitorInvoices();
        });

