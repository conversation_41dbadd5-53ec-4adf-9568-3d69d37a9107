class ExhibitorEventReceiptComponent {
    constructor(ExhibitorsService, ExhibitorsPaymentsService, ExhibitorReceiptsService, ConfirmationService, toastr, APPLICATION_STATUS) {
        this.ExhibitorsService = ExhibitorsService;
        this.ExhibitorsPaymentsService = ExhibitorsPaymentsService;
        this.ExhibitorReceiptsService = ExhibitorReceiptsService;
        this.ConfirmationService = ConfirmationService;
        this.toastr = toastr;

        this.exhibitorReceiptData = {};
        this.initExhibitorReceiptData = {};
        this.total = null;
        this.saveInProcess = false;
        this.isFormSubmitted = false;

        this.PAYMENT_FIELDS = [
            'event_dates',
            'comment',
        ];
        this.APPLICATION_STATUS = APPLICATION_STATUS;
    }

    get formatEventExhibitorInvoice() {
        const exhibitorReceiptData = _.pick(this.exhibitorReceiptData, this.PAYMENT_FIELDS);

        exhibitorReceiptData.booths = this.exhibitorReceiptData.selectedBooths.map(({ id }) => id);
        exhibitorReceiptData.otherBooths = this.ExhibitorsService.generateBoothsForRequest(this.exhibitorReceiptData);
        exhibitorReceiptData.amount = this.total;

        return exhibitorReceiptData;
    }

    async createReceipt() {
        if (this.saveInProcess) {
            return;
        }

        this.saveInProcess = true;
        this.isFormSubmitted = true;

        try {
            this.validateExhibitorReceiptData();

            await this.ExhibitorReceiptsService.createReceipt(
                this.eventId,
                this.formatEventExhibitorInvoice
            );

            this.toastr.success('Exhibitor receipt successfully created');
            this.onClose({ withReload: true });
        } catch (error) {
            if (error.validation) {
                this.toastr.error(error.validation);
            }
        } finally {
            this.saveInProcess = false;
        }
    }

    async updateReceipt() {
        if (this.saveInProcess) {
            return;
        }

        this.saveInProcess = true;
        this.isFormSubmitted = true;

        try {
            this.validateExhibitorReceiptData();

            const receiptSaved = await this.processUpdate();

            if(receiptSaved) {
                this.toastr.success('Exhibitor receipt successfully updated');
            }

            this.onClose({ withReload: true });
        } catch (error) {
            if (error.validation) {
                this.toastr.error(error.validation);
            }
        } finally {
            this.saveInProcess = false;
        }
    }

    validateExhibitorReceiptData() {
        const exhibitorReceiptData = this.exhibitorReceiptData;

        if (_.isEmpty(exhibitorReceiptData)) {
            return;
        }

        if(!this.isCreateMode && !this.isPendingReceipt()) {
            throw {validation: 'You can change only pending receipt'};
        }

        if(this.isCreateMode && this.exhibitorReceiptData && this.exhibitorReceiptData.status !== this.APPLICATION_STATUS.APPROVED) {
            throw { validation: 'The Application Status must be approved to generate an invoice' };
        }

        this.ExhibitorsService.validateExhibitorPaymentData(exhibitorReceiptData);
    }

    async processUpdate() {
        if (this.isRegenerateInvoiceMode()) {
            const shouldRegenerate = await this.confirmInvoiceRegeneration();

            if (!shouldRegenerate) {
                return false;
            }
        }

        return this.updateExhibitorEventReceipt();
    }

    isRegenerateInvoiceMode() {
        return this.ExhibitorsService.isRegenerateInvoiceMode(
            this.exhibitorReceiptData,
            this.initExhibitorReceiptData
        );
    }

    async confirmInvoiceRegeneration() {
        const answer = await this.ConfirmationService.ask(
            'The booths list has changed. Receipt will be re-generated.',
            { disableNoBtn: true }
        );
        return answer === this.ConfirmationService.YES_RESP;
    }

    async updateExhibitorEventReceipt() {
        return this.ExhibitorReceiptsService.updateReceipt(
            this.eventId,
            this.receiptId,
            this.formatEventExhibitorInvoice
        )
    }

    isPendingReceipt() {
        return this.ExhibitorsPaymentsService.isPendingPayment(this.exhibitorReceiptData);
    }

    async onEventChange() {
        this.exhibitorReceiptData = null;
    }

    isApplicationApproved() {
        return (this.exhibitorReceiptData && this.exhibitorReceiptData.status === this.APPLICATION_STATUS.APPROVED);
    }
}

angular.module('SportWrench').component('exhibitorEventReceipt', {
    templateUrl: 'exhibitor/exhibitor-event-receipt/exhibitor-event-receipt.html',
    bindings: {
        eventId: '<',
        receiptId: '<',
        companyName: '<',
        isCreateMode: '<',
        isFormSubmitted: '<',
        events: '<',
        onClose: '&'
    },
    controller: [
        'ExhibitorsService',
        'ExhibitorsPaymentsService',
        'ExhibitorReceiptsService',
        'ConfirmationService',
        'toastr',
        'APPLICATION_STATUS',
        ExhibitorEventReceiptComponent
    ]
});
