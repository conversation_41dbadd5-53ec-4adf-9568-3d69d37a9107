<div class="row">
    <div class="col-sm-12">
        <div class="modal-header">
            {{$ctrl.isCreateMode ? 'Create Invoice' : 'Invoice for ' + $ctrl.companyName}}
        </div>
    </div>
    <div class="col-sm-12">
        <div class="modal-body">
            <div class="row exhibitor-apply-form-application_row" ng-if="$ctrl.isCreateMode">
                <div class="col-sm-3 text-right"><span class="text-bold">Event Name:</span></div>
                <div class="col-sm-9">
                    <div class="form-inline">
                        <div class="row">
                            <div class="col-sm-8">
                                <select
                                    ng-model="$ctrl.eventId"
                                    class="form-control exhibitor-event_registration-form_booth-select"
                                    ng-options="event.event_id as event.name for event in $ctrl.events"
                                    ng-change="$ctrl.onEventChange()"
                                    required>
                                    <option value="" ng-if="!$ctrl.eventId">Select...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <exhibitor-event-receipt-form
                ng-if="!$ctrl.isCreateMode || ($ctrl.isCreateMode && $ctrl.eventId)"
                event-id="$ctrl.eventId"
                receipt-id="$ctrl.receiptId"
                exhibitor-receipt-data="$ctrl.exhibitorReceiptData"
                init-exhibitor-receipt-data="$ctrl.initExhibitorReceiptData"
                total="$ctrl.total"
                on-modal-close="$ctrl.onClose({ withReload: true })"
                is-create-mode="$ctrl.isCreateMode"
                is-form-submitted="$ctrl.isFormSubmitted"
            >
            </exhibitor-event-receipt-form>
        </div>
    </div>
    <div class="col-sm-12">
        <div class="modal-footer">
            <button class="btn btn-default" ng-click="$ctrl.onClose()">Close</button>
            <button ng-if="$ctrl.isCreateMode || (!$ctrl.isCreateMode && $ctrl.isPendingReceipt())"
                    class="btn btn-primary"
                    ng-click="$ctrl.isCreateMode ? $ctrl.createReceipt() : $ctrl.updateReceipt()"
                    ng-disabled="!$ctrl.isApplicationApproved()">
                {{$ctrl.isCreateMode ? 'Create' : 'Update'}}
            </button>
        </div>
    </div>
</div>
