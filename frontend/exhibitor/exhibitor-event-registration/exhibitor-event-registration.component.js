class ExhibitorEventRegistrationComponent {
    constructor(EXHIBITOR_EVENT_TABS, ExhibitorService, ExhibitorsService, toastr) {
        this.tabs = EXHIBITOR_EVENT_TABS;
        this.ExhibitorService = ExhibitorService;
        this.ExhibitorsService = ExhibitorsService;
        this.toastr = toastr;

        this.registrationData = {};
        this.saveInProcess = false;
        this.total = null;
        this.isFormSubmitted = false;

        this.EDIT_FIELDS = [
            'is_sponsor',
            'is_exhibitor',
            'is_non_profit',
            'is_other'
        ];

        this.APPLY_FIELDS = [
            ...this.EDIT_FIELDS,
            'event_dates',
            'comment',
        ];
    }

    $onInit() {
        this.initTab();
    }

    get formattedRegistrationData() {
        if(!this.isApplyMode) {
            return _.pick(this.registrationData, this.EDIT_FIELDS);
        }

        const registrationData = _.pick(this.registrationData, this.APPLY_FIELDS);
        registrationData.booths = this.registrationData.selectedBooths.map(({ id }) => id).filter((id) => id);
        registrationData.otherBooths = this.ExhibitorsService.generateBoothsForRequest(this.registrationData);
        registrationData.amount = this.total;

        return registrationData;
    }

    initTab() {
        this.currentTab = this.tab;
    }

    onTabSelect(tab) {
        this.currentTab = tab;
    }

    loadTabContent(tab) {
        return this.currentTab === tab;
    }

    getSubmitBtnName() {
        if (this.isApplyMode) {
            return 'Apply';
        }

        if (this.isEditMode) {
            return 'Edit Info';
        }

        if (this.isViewMode) {
            return 'View Info';
        }
    }

    getCloseBtnName() {
        if (this.currentTab === this.tabs.EVENT_INFO) {
            return 'Close';
        }

        if (this.currentTab === this.tabs.REGISTRATION_INFO) {
            return 'Cancel';
        }
    }

    showSubmitButton() {
        if (!this.showApplicationTab()) {
            return false;
        }

        if (this.isViewMode && this.currentTab === this.tabs.EVENT_INFO) {
            return false;
        }

        if (this.currentTab === this.tabs.REGISTRATION_INFO) {
            return this.isApplyMode || this.isEditMode;
        } else if (this.currentTab === this.tabs.EVENT_INFO) {
            return true;
        }
    }

    onSubmitSuccess(notification) {
        this.toastr.success(notification);

        this.onCloseModal({ withReload: true });
    }

    onApply() {
        if (this.currentTab === this.tabs.EVENT_INFO) {
            this.currentTab = this.tabs.REGISTRATION_INFO;

            return;
        }

        if (this.currentTab === this.tabs.REGISTRATION_INFO) {
            if (this.saveInProcess) {
                return;
            }

            this.saveInProcess = true;
            this.isFormSubmitted = true;

            try {
                this.validateRegistrationInfo();

                if (this.isApplyMode) {
                    return this.ExhibitorService.create(this.eventId, this.formattedRegistrationData)
                        .then(this.onSubmitSuccess.bind(this, 'Successfully created'));
                }

                if (this.isEditMode) {
                    return this.ExhibitorService.update(this.eventId, this.formattedRegistrationData)
                        .then(this.onSubmitSuccess.bind(this, 'Successfully updated'))
                }
            } catch (error) {
                if (error && error.validation) {
                    this.toastr.error(error.validation);
                }
            } finally {
                this.saveInProcess = false;
            }
        }
    }

    validateRegistrationInfo() {
        const registrationData = this.registrationData;

        if (!this.isApplyMode || _.isEmpty(registrationData)) {
            return;
        }

        if (this.ExhibitorsService.isPurchaseDataRequired(registrationData)) {
            this.ExhibitorsService.validateExhibitorPaymentData(registrationData);
        }
    }

    showApplicationTab() {
        return !(!this.status && !this.exhibitorsRegistrationIsOpened);
    }
}

angular.module('SportWrench').component('exhibitorEventRegistration', {
    templateUrl: 'exhibitor/exhibitor-event-registration/exhibitor-event-registration.html',
    bindings: {
        eventName: '<',
        onCloseModal: '&',
        tab: '<',
        eventId: '<',
        isApplyMode: '<',
        isEditMode: '<',
        isViewMode: '<',
        status: '<',
        exhibitorsRegistrationIsOpened: '<',
        isFormSubmitted: '<',
    },
    controller: [
        'EXHIBITOR_EVENT_TABS',
        'ExhibitorService',
        'ExhibitorsService',
        'toastr',
        ExhibitorEventRegistrationComponent
    ]
});
