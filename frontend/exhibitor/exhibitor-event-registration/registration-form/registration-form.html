<div class="row">
    <div class="col-sm-12">
        <spinner active="$ctrl.loading.inProcess"></spinner>
        <uib-alert type="danger text-center" ng-if="$ctrl.loading.error">{{$ctrl.loading.error}}</uib-alert>
    </div>
    <div ng-if="!$ctrl.loading.inProcess && !$ctrl.loading.error" class="col-sm-12">
        <form class="exhibitor-event_registration-form">
            <fieldset ng-disabled="$ctrl.isViewMode">
                <div class="row exhibitor-apply-form-application_row_status">
                    <div class="col-sm-3 text-bold text-right"><span class="text-bold">Status:</span></div>
                    <div class="col-sm-9">
                        <exhibitor-apply-form-status status="$ctrl.info.status" disabled="{{$ctrl.disabledStatus}}"></exhibitor-apply-form-status>
                    </div>
                </div>
                <div class="row exhibitor-event_registration-form_row">
                    <div class="col-sm-3 text-right"><span class="text-bold">Vendor Type:</span></div>
                    <div class="col-sm-9">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="exhibitor-event_registration-form_vendor-item">
                                    <label class="checkbox-inline">
                                        <input ng-disabled="!$ctrl.info.allowed_vendor_types.is_exhibitor" ng-model="$ctrl.info.is_exhibitor" type="checkbox">
                                        <span>Exhibitor</span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="exhibitor-event_registration-form_vendor-item">
                                    <label class="checkbox-inline">
                                        <input ng-disabled="!$ctrl.info.allowed_vendor_types.is_sponsor" ng-model="$ctrl.info.is_sponsor" type="checkbox">
                                        <span>Sponsor</span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="exhibitor-event_registration-form_vendor-item">
                                    <label class="checkbox-inline">
                                        <input ng-disabled="!$ctrl.info.allowed_vendor_types.is_non_profit" ng-model="$ctrl.info.is_non_profit" type="checkbox">
                                        <span>Non Profit</span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="exhibitor-event_registration-form_vendor-item">
                                    <label class="checkbox-inline">
                                        <input ng-disabled="!$ctrl.info.allowed_vendor_types.is_other" ng-model="$ctrl.info.is_other" type="checkbox">
                                        <span>Other</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div ng-class="{'row exhibitor-event_registration-form_row': true, 'validation-required': $ctrl.isPurchaseDataRequired()}"
                     ng-if="$ctrl.isApplyMode && $ctrl.info.event_booths && $ctrl.info.event_booths.length > 0">
                    <label class="col-sm-3 control-label text-right">
                        Dates:
                    </label>
                    <div class="col-sm-9">
                        <div class="row">
                            <div ng-repeat="(key, value) in $ctrl.info.event_dates" class="col-sm-6">
                                <div class="exhibitor-event_registration-form_dates-item">
                                    <label class="checkbox-inline">
                                        <input ng-model="$ctrl.info.event_dates[key]" type="checkbox">
                                        <span>{{key | UTCdate: $ctrl.EVENT_DATES_FORMAT}}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <span class="text-danger" ng-if="$ctrl.isPurchaseDataRequired() && $ctrl.isEventDatesEmpty() && $ctrl.isFormSubmitted">
                            Please select at least one date
                        </span>
                    </div>
                </div>
                <div ng-class="{'row exhibitor-event_registration-form_row': true, 'validation-required': $ctrl.isPurchaseDataRequired()}"
                     ng-if="$ctrl.isApplyMode && $ctrl.info.event_booths && $ctrl.info.event_booths.length > 0">
                    <label class="col-sm-3 control-label text-right">
                        Booth:
                    </label>
                    <div class="col-sm-9">
                        <div class="form-inline">
                            <div class="row">
                                <div class="col-sm-8">
                                    <select
                                            ng-model="$ctrl.selectedBooth"
                                            class="form-control exhibitor-event_registration-form_booth-select"
                                            ng-options="booth as booth.label for booth in $ctrl.info.event_booths"
                                    >
                                        <option value="" selected>Choose a Booth...</option>
                                    </select>
                                    <div class="exhibitor-event_registration-form_selected-booths" ng-if="$ctrl.selectedBooths.length">
                                        <div class="exhibitor-event_registration-form_selected-booths_item" ng-repeat="booth in $ctrl.selectedBooths track by $index">
                                            <span>{{booth.label}}</span>
                                            <span class="pointer text-danger" ng-click="$ctrl.removeBooth($index)"><i class="fa fa-remove"></i></span>
                                        </div>
                                    </div>
                                    <div class="row" ng-if="$ctrl.isPurchaseDataRequired() && $ctrl.isBoothsEmpty() && $ctrl.isFormSubmitted">
                                        <div class="col-sm-12">
                                            <span class="text-danger">Please add at least one booth</span>
                                        </div>
                                    </div>
                                </div>
                                <div ng-if="$ctrl.info.event_booths.length" class="col-sm-4">
                                    <button ng-click="$ctrl.addBooth()" class="btn btn-primary to-right">Add Booth</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row exhibitor-event_registration-form_row"
                     ng-if="$ctrl.isApplyMode && $ctrl.info.event_booths && $ctrl.info.event_booths.length > 0">
                    <div class="col-sm-3 text-right"><span class="text-bold">Comment:</span></div>
                    <div class="col-sm-9">
                        <textarea ng-model="$ctrl.info.comment" class="full-width form-control" rows="2">{{$ctrl.info.comment}}</textarea>
                    </div>
                </div>
                <div ng-if="$ctrl.selectedBooths.length && $ctrl.isApplyMode" class="row">
                    <div class="col-sm-3 text-right"><span class="text-bold">Total:</span></div>
                    <div class="col-sm-9">
                        <span class="text-bold">{{$ctrl.getBoothsTotal() | currency}}</span>
                    </div>
                </div>
            </fieldset>
        </form>
    </div>
</div>
