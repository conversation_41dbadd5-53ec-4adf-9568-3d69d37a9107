class RegistrationFormComponent {
    constructor(ExhibitorService, ExhibitorsService, UtilsService, INTERNAL_ERROR_MSG, EVENT_DATES_FORMAT) {
        this.ExhibitorService = ExhibitorService;
        this.ExhibitorsService = ExhibitorsService;
        this.UtilsService = UtilsService;
        this.INTERNAL_ERROR_MSG = INTERNAL_ERROR_MSG;
        this.EVENT_DATES_FORMAT = EVENT_DATES_FORMAT;

        this.loading = {
            inProcess: true,
            error: '',
        }

        this.info = {};
        this.selectedBooth = null;
        this.disabledStatus = true;
    }

    $onInit() {
        this.loadInfo();
    }

    loadInfo() {
        this.ExhibitorService.getRegistrationInfo(this.eventId)
            .then(this.onGetRegistrationInfoSuccess.bind(this))
            .catch(this.onGetRegistrationInfoError.bind(this))
            .finally(this.onGetRegistrationInfoFinally.bind(this));
    }

    onGetRegistrationInfoSuccess(response) {
        this.info = response;

        this.selectedBooths = this.info.booths || [];

        this.registrationData = this.info;
        this.registrationData.selectedBooths = this.selectedBooths;
    }

    onGetRegistrationInfoError(error) {
        this.loading.error = error && error.validation
            ? error.validation
            : this.INTERNAL_ERROR_MSG;
    }

    onGetRegistrationInfoFinally() {
        this.loading.inProcess = false;
    }

    addBooth() {
        if (!this.selectedBooth) {
            return;
        }

        this.selectedBooths.push(this.selectedBooth);
    }

    removeBooth(index) {
        this.selectedBooths.splice(index, 1);
    }

    getBoothsTotal() {
        if (this.selectedBooths.length) {
            const total = this.selectedBooths.reduce((acc, { amount }) => {
                return acc + amount;
            }, 0)

            return this.total = this.UtilsService.approxNumber(total);
        }
    }

    isPurchaseDataRequired() {
        return this.ExhibitorsService.isPurchaseDataRequired(this.registrationData);
    }

    isEventDatesEmpty() {
        const {event_dates = {}} = this.registrationData || {};

        return !this.ExhibitorsService.isEventDatesSelected(event_dates);
    }

    isBoothsEmpty() {
        return _.isEmpty(this.selectedBooths);
    }
}

angular.module('SportWrench').component('exhibitorEventRegistrationForm', {
    templateUrl: 'exhibitor/exhibitor-event-registration/registration-form/registration-form.html',
    bindings: {
        total: '=',
        eventId: '<',
        registrationData: '=',
        isViewMode: '<',
        isApplyMode: '<',
        isFormSubmitted: '<',
    },
    controller: [
        'ExhibitorService',
        'ExhibitorsService',
        'UtilsService',
        'INTERNAL_ERROR_MSG',
        'EVENT_DATES_FORMAT',
        RegistrationFormComponent
    ]
});
