angular.module('SportWrench')

.controller('Club.EditMasterAthleteController', EditAthleteController);

function EditAthleteController (
    $rootScope, $scope, athletesService, $filter, toastr, moment,
    geoService, athleteId, data, DateService, masterClubService,
    MALE_CHAR, FEMALE_CHAR, INVALID_FORM_ERROR_MSG, SANCTIONING_BODY, GENDER_VALUES
) {
    $scope.GENDER_VALUES = GENDER_VALUES;

    $scope.teams            = [{
        id: -1,
        name: 'Unassigned'
    }];
    $scope.heights          =  generateHeightArray();
    $scope.utils            = {
        selection: {}
    };

    $scope.dateOptions       = {};
    $scope.status            = { opened: false };

    $scope.geo_data = {
        countries: []
    };

    $scope.athlete              = data.athlete;
    $scope.states               = data.states;
    $scope.positions            = data.positions;
    $scope.roles                = data.roles;
    $scope.teams                = $scope.teams.concat(data.teams);
    $scope.rosterAthletes       = data.rosterAthletes;
    $scope.athlete.birthdate    = new Date($scope.athlete.birthdate);
    $scope.athlete.gradyear     = String(data.athlete.gradyear);
    $scope.blockedEvents        = data.blocked_events;

    $scope.clubHasAauSanctioning = masterClubService.clubHasAauSanctioning($scope.club);

    if(!$scope.athlete.master_team_id) {
        $scope.athlete.master_team_id = -1;
    }

    var master_athlete_id = athleteId,
        getGenderIcon = function (gender) {
            switch(gender) {
                case 'female':
                    return FEMALE_CHAR;
                case 'male':
                    return MALE_CHAR;
                case 'coed':
                    return (MALE_CHAR + FEMALE_CHAR);
                default:
                    return '';
            }
        };   
    
    geoService.getCountries().then(function (data) {
        $scope.geo_data.countries = data;
    });

    $scope.getStates = function(countryCode) {
        geoService.getStates(countryCode, function(res) {
            $scope.states = res.data;
        });
    };  

    $scope.team_name = function (t) {
        return t.name + ' '+ getGenderIcon(t.gender);
    };

    $scope.hasAauSanctioning = function () {
        return $scope.loadClub && $scope.loadClub.sport_sanctionings
            && $scope.loadClub.sport_sanctionings.includes(SANCTIONING_BODY.AAU);
    };

    $scope.updateAthlete = function () {
        if($scope.juniorForm.$invalid) {
            toastr.warning(INVALID_FORM_ERROR_MSG);
            return;
        }

        var a = _.omit(this.athlete,
            'master_athlete_id', 'organization_code', 'last', 'gender', 'membership_status', 'team_name',
            'ethnicity', 'usav_number', 'aau_membership_id');

        a.gradyear      = parseInt(a.gradyear, 10);

        if(!_.isNull(a.jersey) && a.jersey <= 0) {
            toastr.warning('Uniform must be a positive number');
            return;
        }

        if(!_.isNull(a.aau_jersey) && a.aau_jersey <= 0) {
            toastr.warning('AAU Uniform must be a positive number');
            return;
        }

        a.birthdate     = moment(a.birthdate).format('MM/DD/YYYY');
    
        changeEmptyFields(a);
        
        athletesService.updateAthlete(master_athlete_id, a).success(function (data) {
            var _updated_athlete = data && data.athlete;
            if(!_.isEmpty(_updated_athlete)) {
                $scope.$emit('club.athletes.replace-athlete', _updated_athlete);
                $scope.$close();
            }
            toastr.success('Athlete information has been successfully edited');
        }).error(function (data) {
            if(data.validation) {
                toastr.error(data.validation);
            }
        });
    };

    $scope.disableState = function() {
        // [Canada, United States]
        const countries = ['CA', 'US'];

        return !_.includes(countries, $scope.athlete.country);
    };

    function generateHeightArray () {
        var heights = [{
                "v": "",
                "t": "Select Height..."
            }],
            max = 90;
        for (var i = 54; i <= max; ++i) {
            heights.push({
                "v": i,
                "t": (parseInt(i / 12, 10) + '\'' + ('0' + (i % 12)).slice(-2) + '\"')
            });
        }
        return heights;
    }
    
    function changeEmptyFields(data) {
        _.each(data, (val, key) => {
            if(data[key] === '') {
                data[key] = null;
            }
        });
    }
}
