/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE INDEX IF NOT EXISTS master_athlete_season_membership_status_usav_number_idx
            ON master_athlete(season, membership_status, usav_number)
            WHERE deleted IS NULL;

        CREATE INDEX IF NOT EXISTS master_staff_season_membership_status_usav_number_idx
            ON master_staff(season, membership_status, usav_number)
            WHERE deleted IS NULL;
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        DROP INDEX IF EXISTS master_athlete_season_membership_status_usav_number_idx;

        DROP INDEX IF EXISTS master_staff_season_membership_status_usav_number_idx;
    `)
};
