<event-member-header
    first="member.first"
    last="member.last"
    event-name="{{eventName}}"
    team-name="{{teamName}}"
    roster-locked="rosterLocked"
    deadline-passed="deadlinePassed"
    deadline="deadline"
></event-member-header>
<div class="modal-body event-member-modal">
	<deletion-sign 
		ng-if="member.deleted_by_user" 
		deleted-at="member.deleted_by_user" 
		cancel="reinstate()" 
		disable="rosterLocked">
	</deletion-sign>
    <div class="row" ng-if="memberLoaded()">
        <div class="col-sm-offset-2 col-sm-5">
            <div><strong>USAV Code:</strong> {{member.usav_code}}</div>
            <div><strong>Gender:</strong> {{member.gender}}</div>
            <div><strong>Birthdate:</strong> {{member.birthdate}}</div>
            <div><strong>Membership:</strong> {{member.membership_status}}</div>

            <div ng-if="isJunior()"><strong>Gradyear:</strong> {{member.gradyear}}</div>
            <div ng-if="isJunior()"><strong>Age:</strong> {{member.age}}</div>
            <div ng-if="clubHasNineManSanctioning && isJunior()"><strong>Athlete Role:</strong> {{member.role}}</div>

            <div ng-if="!isJunior()"><strong>Certification:</strong> {{member.cert}}</div>
            <div ng-if="!isJunior()"><strong>SafeSport:</strong> {{member.ss_status}}</div>
            <div ng-if="!isJunior()"><strong>BG Expires:</strong> {{member.bg_expire_date}}</div>
        </div>
        <div class="col-sm-5">
            <button ng-if="!hideMemberInfoButton()"
                    class="btn btn-info"
                    ng-click="openMemberInfo()"
            >Edit {{isJunior()?'Athlete':'Staffer'}} Info</button>
        </div>
    </div>
    <hr/>
	<event-member-edit type="{{memberType}}"></event-member-edit>
    <hr/>
	<uib-alert type="danger" ng-if="utils.error">{{utils.error}}</uib-alert>
    <div class="row" ng-if="!(member.deleted_by_user || rosterLocked)">
        <div class="col-sm-4 center-form-text">
            <a href="" class="text-danger" ng-click="withdraw()">Remove from this Event</a>
        </div>
        <div class="col-sm-offset-1 col-sm-2">
            
            <button class="btn btn-primary" ng-click="save()">Save</button>
        </div>
    </div> 
    <p class="text-danger text-center" ng-if="deadlinePassed && rosterLocked">
        Deadline passed at {{getDeadline()}}.<br/> <i class="fa fa-exclamation-triangle"></i> Team Roster is Locked.
    </p>
    <p class="text-danger text-center" ng-if="!deadlinePassed && rosterLocked">
        <span ng-if="onlineCheckinDate">Team checked in online at {{onlineCheckinDate}}.<br/></span>
        <i class="fa fa-exclamation-triangle"></i> Team Roster is Locked.
    </p>
</div>
<div class="modal-footer">
    <button class="btn btn-default" ng-click="$parent.$dismiss()">Close</button>
</div>
