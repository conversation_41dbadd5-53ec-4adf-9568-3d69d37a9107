'use strict';
const SportEngineUtilsService = require('../../lib/SEUtilsService'),
    { ENTRY_STATUSES, ROSTER_TEAM_GENDER } = require('../../constants/teams');

/**
 * Controller to export events data from SW to UA
 *
 */

module.exports = {
    events: function (req, res) {
        var query =
            `SELECT e.event_id,
                e.name, e.long_name,
                e.date_start, e.date_end
             FROM "event" e
             WHERE e.deleted IS NULL
               AND e.is_test = FALSE
               AND e.allow_teams_registration = TRUE
               AND e.season >= $1
             ORDER BY e.date_start
            `;
        Db.query(query, [sails.config.sw_season.current]).then(function (result) {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.send(err);
        })
    },

    clubs: function (req, res) {
        
        let event_id = req.param('event');
        if(!event_id) return res.send('No event identifier passed');
    
        let gender = req.param('gender');
        if(!gender) return res.send('No gender passed');
    
        const dbGender = _mapUAGenderToDb(gender);

        let query = `
            SELECT
              rc.master_club_id,
              rc.roster_club_id,
              rc.club_name,
              rc.code,
              rc.region,
              rc.address,
              rc.city,
              rc.state,
              rc.zip,
              COUNT(rt.roster_team_id) teams,
              mc.director_email,
              mc.administrative_email
            FROM
              roster_club rc
              INNER JOIN roster_team rt ON rc.roster_club_id = rt.roster_club_id
              AND rt.event_id = $1
              AND rt.gender = $2
              AND rt.status_entry = $3
              AND rt.deleted IS NULL
              LEFT JOIN master_club mc ON mc.master_club_id = rc.master_club_id
            WHERE
              rc.event_id = $1
              AND rc.deleted IS NULL
            GROUP BY
              rc.master_club_id,
              rc.roster_club_id,
              rc.club_name,
              rc.code,
              rc.region,
              rc.address,
              rc.city,
              rc.state,
              rc.zip,
              mc.director_email,
              mc.administrative_email
            ORDER BY
              rc.club_name;
        `;
        
        Db.query(query, [event_id, dbGender, ENTRY_STATUSES.ACCEPTED]).then(function (result) {
            let clubs = result.rows;
            if(!clubs.length) {
                res.send('No clubs for event ' + event_id);
            } else {
                res.status(200).json(clubs);
            }
        }).catch(err => {
            res.send(err);
        })
        
    },

    teams: function (req, res) {
        var event_id = req.param('event');
        if(!event_id) return res.send('No event identifier passed');

        var gender = req.param('gender');
        if(!gender) return res.send('No gender passed');

        const dbGender = _mapUAGenderToDb(gender);

        var query =
            ' SELECT rt.roster_team_id, rt.organization_code, rt.team_name, rt.age, rt.rank, ' +
            '   rc.master_club_id, rt.master_team_id, ' +
            '   rt.roster_club_id, rt.division_id, d.name division_name, d.short_name division_short_name, ' +
            '   rt.modified ' +
            ' FROM roster_team rt ' +
            '  LEFT JOIN division d ON d.division_id = rt.division_id ' +
            '  INNER JOIN roster_club rc ON rc.roster_club_id = rt.roster_club_id ' +
            ' WHERE rt.event_id = $1 ' +
            '   AND rt.gender = $2 ' +
            '   AND rt.status_entry = 12 ' +
            '   AND rt.deleted IS NULL ';

        Db.query(query, [event_id, dbGender]).then(function (result) {
            var teams = result.rows;
            if(!teams.length) {
                res.send('No teams for event ' + event_id);
            } else {
                res.status(200).json(teams);
            }
        }).catch(err => {
            res.send(err);
        })
    },

    athletes: function (req, res) {
        var event_id = req.param('event');
        if(!event_id) return res.send('No event identifier passed');

        var gender = req.param('gender');
        if(!gender) return res.send('No gender passed');

        const dbGender = _mapUAGenderToDb(gender);

        var query =
            `SELECT ma.master_athlete_id, 
                   rc.roster_club_id club_id, rt.roster_team_id team_id, 
                   rc.master_club_id, rt.master_team_id, 
                   ma.modified, ma.deleted, 
                   ma.first, ma.last, 
                   ma.organization_code, 
                   COALESCE(ma.phonem, wa.cell_phone) phonem,  
                   COALESCE(ma.phoneh, wa.home_phone) phoneh, 
                   COALESCE(ma.address, wa.address) address, 
                   COALESCE(ma.address2, wa.address2) address2, 
                   COALESCE(ma.city, wa.city) city, 
                   COALESCE(ma.state, wa.state) state, 
                   COALESCE(ma.zip, wa.zip) zip, 
                   COALESCE(ma.email, wa.email) email, 
                   COALESCE(ma.birthdate, wa.birthdate) birthdate, 
                   COALESCE(ma.gradyear, wa.gradyear) gradyear, 
                   COALESCE(ma.gender::VARCHAR, wa.gender) gender, 
                   COALESCE(ma.height, wa.height) height,
                   COALESCE(ra.jersey, ma.jersey) "jersey", 
                   COALESCE(ra.sport_position_id, ma.sport_position_id) "sport_position_id",
                   COALESCE(sp.short_name, spm.short_name) "position" 
            FROM roster_team rt 
            INNER JOIN roster_athlete ra 
                ON ra.roster_team_id = rt.roster_team_id 
                AND ra.deleted IS NULL 
                AND ra.deleted_by_user IS NULL
            INNER JOIN master_athlete ma 
                ON ma.master_athlete_id = ra.master_athlete_id 
            INNER JOIN roster_club rc 
                ON rc.master_club_id = ma.master_club_id 
                AND rc.event_id = rt.event_id 
            LEFT JOIN sport_position sp 
                ON sp.sport_position_id = ra.sport_position_id 
            LEFT JOIN "sport_position" spm 
                ON spm.sport_position_id = ma.sport_position_id
            LEFT JOIN webpoint_athlete wa 
                ON wa.usav_code = ma.organization_code 
                AND rc.master_club_id = wa.master_club_id 
            WHERE rt.event_id = $1 
                AND rt.gender = $2 
                AND rt.deleted IS NULL 
                AND rt.status_entry = 12 
                AND rc.deleted IS NULL ` //+
            //'   AND ma.deleted IS NULL ' // i think we should return an athlete event if master row deleted
            ;

        Db.query(query, [event_id, dbGender]).then(function (result) {
            var rows = result.rows;

            rows.forEach(function(row){
               row.height = WebpointUtility.getHeightInInches(row.height);
            });

            res.status(200).json(rows);
        }).catch(err => {
            res.send(err);
        })
    },

    staff: function (req, res) {
        var event_id = req.param('event');
        if(!event_id) return res.send('No event identifier passed');

        var gender = req.param('gender');
        if(!gender) return res.send('No gender passed');

        const dbGender = _mapUAGenderToDb(gender);

        var query =
            `SELECT ms.master_staff_id, 
                    ms.modified, 
                    ms.master_staff_id staff_id, 
                    ms.master_club_id, 
                    COALESCE(r.name, rm.name) "role", 
                    COALESCE(r.role_id, rm.role_id) "role_id", 
                    ms.first, ms.last, 
                    ms.address, ms.city, ms.state, ms.zip, 
                    ms.email, ms.phone, 
                    rt.roster_team_id team_id,
                    rt.master_team_id,
                    ms.organization_code usav_code
            FROM roster_team rt 
            INNER JOIN roster_staff_role rsr 
                ON rsr.roster_team_id = rt.roster_team_id 
                AND rsr.deleted IS NULL 
                AND rsr.deleted_by_user IS NULL
            LEFT JOIN "master_staff_role" msr 
                ON msr.master_staff_id = rsr.master_staff_id 
                AND msr.master_team_id = rsr.master_team_id
            INNER JOIN master_staff ms 
                ON ms.master_staff_id = rsr.master_staff_id
            LEFT JOIN "role" rm 
                ON rm.role_id = msr.role_id 
            INNER JOIN role r 
                ON r.role_id = rsr.role_id 
            WHERE rt.event_id = $1 
              AND rt.gender = $2 
              AND rt.status_entry = 12 
              AND rt.deleted IS NULL 
              AND rsr.deleted IS NULL `;

        Db.query(query, [event_id, dbGender]).then(function (result) {

            var rows = result.rows;

            res.status(200).json(rows);
        }).catch(err => {
            res.send(err);
        })
    },

    schedule: function (req, res) {
        var event_id = req.param('event');
        if(!event_id) return res.send('No event identifier passed');

        var gender = req.param('gender');
        if(!gender) return res.send('No gender passed');

        const dbGender = _mapUAGenderToDb(gender);

        UAExportService.getSchedule({eventID: event_id, gender: dbGender }).then(function (rows) {
            if(!rows.length) {
                res.status(200).send('No schedule for event ' + event_id);
            } else {
                res.status(200).json(rows);
            }
        }).catch(err => {
            res.send(err);
        })
    },

    // GET /api/ua/export/:event/locations
    findEventLocations: function (req, res) {
        let eventID = Number(req.params.event);

        if(!eventID) {
            return res.validation('No event identifier passed');
        }

        let query = squel.select().from('event_location', 'el')
            .field('el.name')
            .field('el.short_name')
            .field('el.address')
            .field('el.city')
            .field('el.state')
            .field('el.zip')
            .field('el.courts_from')
            .field('el.courts_to')
            .where('el.event_id = ?', eventID);

        Db.query(query).then(result => result.rows)
            .then(rows => {
                res.status(200).json(rows);
            })
            .catch(err => {
                res.customRespError(err, {status: 400});
            })
    },

    // GET /api/ua/export/member/:usav/find
    findUSAVData: async function (req, res) {
        const USAV_REGEX = /[0-9]{7,7}/;
        const usavCode = req.params.usav;
        const birthDate = req.query.birth_date;

        if (!usavCode) {
            return res.validation('No USAV Code provided');
        }

        if (!birthDate) {
            return res.validation('No birth date provided');
        }

        let usavNumber = USAV_REGEX.exec(usavCode);

        usavNumber = usavNumber && usavNumber[0];

        if (!usavNumber) {
            return res.validation('Invalid USAV Code');
        }

        try {
            const members = [];
            for await (const member of SportsEngineService.getEligibility({
                [SportEngineUtilsService.SE_FIELDS.USAV_CODE]: usavCode,
                [SportEngineUtilsService.SE_FIELDS.BIRTHDATE]: birthDate,
            })) {
                members.push(member)
            }
            res.status(200).json(members);
        }
        catch (err) {
            res.customRespError(err);
        }
    }
};

function _mapUAGenderToDb(gender) {
    const normalizedGender = gender?.toLowerCase();
    const genderMap = {
        f: ROSTER_TEAM_GENDER.FEMALE,
        m: ROSTER_TEAM_GENDER.MALE,
        female: ROSTER_TEAM_GENDER.FEMALE,
        male: ROSTER_TEAM_GENDER.MALE,
        coed: ROSTER_TEAM_GENDER.COED,
    };

    const dbGender = genderMap[normalizedGender];

    if(!dbGender) {
        throw new Error(`Unsupported gender from UA ${gender}. Supported genders are ${Object.keys(genderMap)}`)
    }

    return dbGender;
}
