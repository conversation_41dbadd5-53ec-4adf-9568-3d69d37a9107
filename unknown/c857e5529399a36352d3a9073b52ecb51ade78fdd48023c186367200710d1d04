
const historyActions = require('./history-actions');

const {
    MEMBER_TYPE,
    CANCELED_MEMBER_STATUS,
} = require('../../../api/lib/SEUtilsService');

class StaffersProcessor {
    #LIMIT = 100;

    async getMembers(season) {
        const query = `
            SELECT 
                (
                    SELECT coalesce(array_to_json(array_agg(row_to_json(teams))), '[]'::json)
                    FROM (
                        SELECT 
                            rsr.roster_staff_role_id,
                            rt.roster_team_id,
                            rt.roster_club_id,
                            e.event_id
                        FROM roster_staff_role rsr
                        JOIN roster_team rt 
                            ON rt.roster_team_id = rsr.roster_team_id 
                            AND rt.deleted IS NULL
                            AND rt.locked IS FALSE    
                        JOIN event e 
                            ON e.event_id = rt.event_id
                            AND e.deleted IS NULL
                            AND e.date_start > now() AT TIME ZONE e.timezone
                        WHERE rsr.master_staff_id = ms.master_staff_id
                            AND rsr.deleted IS NULL
                            AND rsr.deleted_by_user IS NULL
                    ) teams
                ) AS event_roster_teams,
                (
                    SELECT COALESCE(array_to_json(array_agg(teams)), '[]'::json)
                    FROM (
                        SELECT 
                            msr.master_staff_role_id,
                            msr.master_team_id
                        FROM master_staff_role msr
                        WHERE msr.master_staff_id = ms.master_staff_id
                    ) teams
                ) AS club_roster_teams,
                ms.aau_membership_id IS NOT NULL AS has_aau_membership,
                ms.master_staff_id,
                ms.master_club_id
            FROM master_staff ms
            WHERE ms.usav_number IS NOT NULL
                AND ms.membership_status = $1
                AND ms.deleted IS NULL
                AND ms.season = $2
            LIMIT $3
        `;

        const { rows } = await Db.query(query, [
            CANCELED_MEMBER_STATUS,
            season,
            this.#LIMIT
        ]);

        return rows || [];
    }

    async processMember(memberData) {
        this.#validateMemberData(memberData);

        let tr = null;

        try {
            tr = await Db.begin();

            if (!_.isEmpty(memberData.event_roster_teams)) {
                await this.#removeMemberFromEventRoster(tr, memberData.event_roster_teams);
                await this.#saveEventRosterDeletingHistoryActions(tr, memberData);
            }

            if (!_.isEmpty(memberData.club_roster_teams) && !memberData.has_aau_membership) {
                await this.#removeMemberFromClubRoster(tr, memberData.club_roster_teams);
                await this.#saveClubRosterDeletingHistoryActions(tr, memberData);
            }

            const preparedStaffClubData = await this.#prepareStaffClubData(memberData);

            await this.#updateStafferClubRow(tr, memberData.master_staff_id, preparedStaffClubData);

            if (preparedStaffClubData.deleted) {
                await historyActions.saveStaffDeletedAction(tr, memberData);
            } else {
                await historyActions.saveStaffUSAVDataRemovedAction(tr, memberData);
            }

            await tr.commit();
        } catch (err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async #removeMemberFromEventRoster(tr, eventRosterTeams) {
        if (!tr) {
            throw new Error('Transaction should be defined');
        }

        if (_.isEmpty(eventRosterTeams)) {
            throw new Error(`eventRosterTeams can't be empty`);
        }

        const rosterStaffRoleIDs = eventRosterTeams.map(team => team.roster_staff_role_id);

        const query = knex('roster_staff_role')
            .update({ deleted: knex.fn.now() })
            .whereNull('deleted')
            .whereNull('deleted_by_user')
            .whereIn('roster_staff_role_id', rosterStaffRoleIDs);

        return tr.query(query);
    }

    async #removeMemberFromClubRoster(tr, clubRosterTeams) {
        if (!tr) {
            throw new Error('Transaction should be defined');
        }

        if (_.isEmpty(clubRosterTeams)) {
            throw new Error(`clubRosterTeams can't be empty`);
        }

        const masterStaffRoleIDs = clubRosterTeams.map(team => team.master_staff_role_id);

        const query = knex('master_staff_role')
            .delete()
            .whereIn('master_staff_role_id', masterStaffRoleIDs);

        return tr.query(query);
    }

    async #updateStafferClubRow(tr, masterStaffID, preparedData) {
        if (!tr) {
            throw new Error('Transaction should be defined');
        }

        if (_.isEmpty(preparedData)) {
            throw new Error(`preparedData couldn't be empty'`);
        }

        const query = knex('master_staff')
            .update(preparedData)
            .where('master_staff_id', masterStaffID)
            .whereNull('deleted');

        return tr.query(query);
    }

    async #prepareStaffClubData(memberData) {
        let data = {};

        if (memberData.has_aau_membership) {
            const fieldsToSetEmpty = SportEngineMemberService
                .import.process
                .memberImport
                .prepareEmptyUsavFields(MEMBER_TYPE.STAFF);

            data = Object.assign(fieldsToSetEmpty, data);
        } else {
            data.deleted = knex.fn.now();
        }

        return data;
    }

    #validateMemberData(memberData = {}) {
        const {
            master_staff_id,
            master_club_id,
            event_roster_teams,
            club_roster_teams,
            has_aau_membership,
        } = memberData;

        if (!master_staff_id) {
            throw new Error('master_staff_id is required');
        }

        if (!master_club_id) {
            throw new Error('master_club_id is required');
        }

        this.#validateEventRosterTeamsData(event_roster_teams);
        this.#validateClubRosterTeamsData(club_roster_teams);

        if (!_.isBoolean(has_aau_membership)) {
            throw new Error('has_aau_membership should be boolean');
        }
    }

    #validateEventRosterTeamsData(eventRosterTeams = []) {
        if (!_.isArray(eventRosterTeams)) {
            throw new Error('eventRosterTeams should be an array');
        }

        if (_.isEmpty(eventRosterTeams)) {
            return;
        }

        eventRosterTeams.forEach((eventRosterTeam = {}) => {
            if (!eventRosterTeam.roster_club_id) {
                throw new Error('roster club id is required');
            }

            if (!eventRosterTeam.roster_staff_role_id) {
                throw new Error('roster staff role id is required');
            }

            if (!eventRosterTeam.event_id) {
                throw new Error('event id is required');
            }
        });
    }

    #validateClubRosterTeamsData(clubRosterTeams = []) {
        if (!_.isArray(clubRosterTeams)) {
            throw new Error('clubRosterTeams should be an array');
        }

        if (_.isEmpty(clubRosterTeams)) {
            return;
        }

        clubRosterTeams.forEach((clubRosterTeam = {}) => {
            if (!clubRosterTeam.master_team_id) {
                throw new Error('master team id is required');
            }

            if (!clubRosterTeam.master_staff_role_id) {
                throw new Error('master staff role id is required');
            }
        });
    }

    async #saveEventRosterDeletingHistoryActions(tr, memberData) {
        await Promise.all(memberData.event_roster_teams.map(async (team) => {
            const historyData = {
                ...team,
                master_staff_id: memberData.master_staff_id
            };

            await historyActions.saveStaffRemovedFromEventRosterAction(tr, historyData);
        }));
    }

    async #saveClubRosterDeletingHistoryActions(tr, memberData) {
        await Promise.all(memberData.club_roster_teams.map(async (team) => {
            const historyData = {
                ...team,
                master_staff_id: memberData.master_staff_id,
                master_club_id: memberData.master_club_id,
            };

            await historyActions.saveStaffRemovedFromClubRosterAction(tr, historyData);
        }));
    }
}

module.exports = new StaffersProcessor();
