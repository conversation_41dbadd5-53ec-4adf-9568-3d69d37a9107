/**
 * ESW project controller (events.sportwrench.com)
 */

const _    = require('lodash');
const swUtils = require('../../lib/swUtils');
const teamsConstants = require('../../constants/teams');

const ESW_LAST_VISITED_COOKIE = 'eswlv';

module.exports = {
    // GET /api/esw/public_sw_events
    public_sw_events: function (req, res) {
        let ESWLink = `${sails.config.urls.esw.baseUrl}/#/events/`,
            SWTLink = `${sails.config.urls.swt.baseUrl}/#/events/`;

        // for assigned tickets mode
        const _SWTLink = `${sails.config.urls.swt.baseUrl}/buy?event=`;

        Promise.all([
            Db.query(
                `SELECT * 
                FROM (
                    SELECT (
                           CASE WHEN e.schedule_published IS TRUE THEN e.esw_id
                                 ELSE e.event_id::VARCHAR
                            END
                        ) "event_id", e.long_name, e.date_end, 
                        e.date_start, e.city, e.state,
                        e.name "short_name",
                        (
                            CASE 
                                WHEN (
                                    e.schedule_published IS TRUE
                                ) THEN 'current'
                                ELSE 'attending'
                            END
                        ) "type",
                        FORMAT('${ESWLink}%s', (
                                CASE WHEN e.schedule_published IS TRUE THEN e.esw_id
                                     ELSE e.event_id::VARCHAR
                                END
                            )
                        ) "link"
                    FROM "event" e   
                    WHERE e.live_to_public IS TRUE 
                    AND e.allow_teams_registration IS TRUE
                    AND e.is_test IS NOT TRUE
                    AND e.deleted IS NULL
                    AND (
                        (e.date_start <= (NOW() AT TIME ZONE e.timezone) 
                                        AND (NOW() AT TIME ZONE e.timezone) <= e.date_end) 
                        OR (e.date_end BETWEEN (NOW() AT TIME ZONE e.timezone) 
                                        AND (NOW() AT TIME ZONE e.timezone) + INTERVAL '1 year')
                    )
                ) "e"
                ORDER BY e.date_start, e.event_id ASC `
            ),
            Db.query(
                `SELECT (
                        CASE WHEN e.schedule_published IS TRUE THEN e.esw_id
                             ELSE e.event_id::VARCHAR
                        END
                    ) "event_id", e.name, e.long_name, e.date_end, 
                    e.date_start, e.city, e.state, e.name "short_name",
                    FORMAT('${ESWLink}%s', (
                            CASE WHEN e.schedule_published IS TRUE THEN e.esw_id
                                 ELSE e.event_id::VARCHAR
                            END
                        )
                    ) "link"
                FROM "event" e  
                WHERE e.published = TRUE
                    AND e.teams_use_clubs_module IS TRUE  
                    AND e.live_to_public IS TRUE 
                    AND e.deleted IS NULL
                    AND e.date_end < (NOW() AT TIME ZONE e.timezone)
                    AND e.is_test IS NOT TRUE
                ORDER BY e.date_start DESC, e.event_id ASC `
            ),
            Db.query(
                `SELECT 
                    e.event_tickets_code "event_id",  e.date_end, e.date_start, e.city, e.state, e.ticket_camps_registration,
                    (CASE 
                        WHEN e.ticket_camps_registration IS TRUE then e.long_name
                        ELSE FORMAT('%s (%s)', e.long_name, 
                                        STRING_AGG(et.current_price::NUMERIC::MONEY::VARCHAR, ' / '))
                     END
                    ) "long_name", (
                        CASE
                            WHEN e.ticket_camps_registration IS TRUE then e.name
                        ELSE
                            FORMAT('%s (%s)', e.name, 
                                        STRING_AGG(et.current_price::NUMERIC::MONEY::VARCHAR, ' / '))
                        END
                    ) "short_name",
                    (
                        CASE
                            WHEN
                                COALESCE((tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) IS TRUE
                            THEN
                               FORMAT('${_SWTLink}%s', e.event_tickets_code)
                            ELSE
                               FORMAT('${SWTLink}%s', e.event_tickets_code)
                        END
                    ) AS link
                FROM "event" e
                INNER JOIN event_ticket et
                    ON et.event_id = e.event_id
                    AND et.published IS TRUE
                WHERE e.date_end >= (NOW() AT TIME ZONE e.timezone)
                    AND e.live_to_public = TRUE
                    AND e.allow_ticket_sales = TRUE
                    AND (NOW() AT TIME ZONE e.timezone) < e.tickets_purchase_date_end  
                    AND (NOW() AT TIME ZONE e.timezone) > e.tickets_purchase_date_start
                    AND e.tickets_published = TRUE
                    AND e.is_test IS NOT TRUE
                    AND e.deleted IS NULL
                GROUP BY e.event_id`
            ),
            // Db.query(`
            //     SELECT
            //         DISTINCT e.event_id,
            //         e.name,
            //         e.long_name,
            //         e.date_end,
            //         e.date_start,
            //         e.city,
            //         e.state,
            //         e.name AS short_name
            //     FROM
            //         "event" e
            //         LEFT JOIN "division" d ON d.event_id = e.event_id
            //         AND d.closed IS NULL
            //         AND d.locked IS NOT TRUE
            //         AND d.published IS TRUE
            //     WHERE
            //         e.registration_method = 'doubles'
            //         AND e.deleted IS NULL
            //         AND e.published = TRUE
            //         AND e.teams_use_clubs_module IS TRUE
            //         AND e.live_to_public IS TRUE
            //         AND e.is_test IS NOT TRUE
            //     GROUP BY
            //         e.event_id
            //     HAVING (COALESCE (MAX (d.date_reg_close),
            //                 e.date_reg_close)
            //             >= (now ()
            //                 AT TIME ZONE e.timezone))
            //     ORDER BY
            //         e.date_start ASC;
            // `),
            Promise.resolve().then(() => {
                let c                   = req.cookies,
                    lastVisitedEvents   = (c && c[ESW_LAST_VISITED_COOKIE])
                        ?c[ESW_LAST_VISITED_COOKIE].split('|')
                        :[];
                lastVisitedEvents   = lastVisitedEvents.reduce((numbers, id) => {
                    let n = parseInt(id, 10);
                    if(!!n) {
                        numbers.push(n);
                    }
                    return numbers;
                }, []);

                if(!lastVisitedEvents.length) {
                    return [];
                } else {
                    return Db.query(
                        `SELECT (
                                CASE WHEN e.schedule_published IS TRUE THEN e.esw_id
                                     ELSE e.event_id::VARCHAR
                                END
                            ) "event_id", e.name, e.long_name, 
                            e.date_end, e.date_start, e.city, e.state, 
                            e.name AS short_name ,
                            FORMAT('${ESWLink}%s', (
                                    CASE WHEN e.schedule_published IS TRUE THEN e.esw_id
                                         ELSE e.event_id::VARCHAR
                                    END
                                )
                            ) "link"
                        FROM "event" e  
                        WHERE e.published = TRUE
                            AND e.teams_use_clubs_module IS TRUE  
                            AND e.deleted IS NULL
                            AND e.live_to_public IS TRUE  
                            AND e.public_teams_visibility <> 'hidden'
                            AND e.event_id in (${lastVisitedEvents.join(', ')}) 
                            AND e.is_test IS NOT TRUE
                        ORDER BY e.date_start DESC`
                    ).then(result => {
                        return result.rows;
                    });
                }
            })
        ]).then(data => {
            let current         = data[0].rows,
                results         = data[1].rows,
                tickets         = __filteringCampsFromTickets(data[2].rows, false),
                camps           = __filteringCampsFromTickets(data[2].rows, true),
                // doubles         = data[3].rows,
                recent          = data[3];

            res.status(200).json({ current, camps, results, tickets, recent })
        }).catch(err => {
            res.customRespError(err);
        });
    },
    
    pb_for_match: function(req, res) {
        var match_uuid = req.param('uuid');

        if(!match_uuid) {
            loggers.errors_log.refError(req, 'Invalid match_uuid');
            return res.send('No match_uuid identifier passed');
        }
        if(!swUtils.isUUID(match_uuid)) {
            return res.validation('Invalid match id');
        }

        var query =
            ` SELECT e.esw_id event_id, m.division_id, m.pool_bracket_id, pb.is_pool
              FROM matches m
                INNER JOIN event e ON e.event_id = m.event_id
                LEFT JOIN poolbrackets pb ON pb.uuid = m.pool_bracket_id
              WHERE m.match_id = $1
              `;

        Db.query(query, [match_uuid]).then(function (result) {
            res.status(200).json(result.rows[0]);
        }).catch(err => {
            res.send(err);
        })
    },
    // GET /api/esw/events
    events: function (req, res) {
        Cache.getResult(req.path, () => Db.query(
            `SELECT ( 
                    CASE 
                        WHEN e.schedule_published IS TRUE THEN e.esw_id 
                        ELSE e.event_id::VARCHAR
                    END
                ) "event_id", 
                e.name, e.long_name, e.date_start, e.date_end,
                e.timezone,
                e.city, e.state, e.address, e.has_coed_teams, 
                e.has_female_teams, e.has_male_teams, e.email, 
                e.website,
                TRUE has_athletes,
                TRUE has_staff,
                (
                    e.date_start <= (NOW() AT TIME ZONE e.timezone) 
                    AND (NOW() AT TIME ZONE e.timezone) <= e.date_end
                    AND e.schedule_published IS TRUE
                    AND e.is_test IS NOT TRUE
                    AND e.deleted IS NULL                    
                ) "current",
                e.schedule_published
             FROM "event" e 
             WHERE e.live_to_public IS TRUE 
                AND e.allow_teams_registration IS TRUE
                AND e.esw_id IS NOT NULL
                and e.registration_method <> 'doubles'
             ORDER BY e.date_start`
        ), {req}).then(function (result) {
            res.json(result.rows || []);
        }, function (err) {
            res.send(err);
        })
    },
    // GET /api/esw/:event/manual-clubs
    manualClubsList: (req, res) => {
        const eventId = req.params.event;

        if(!swUtils.isESWId(eventId)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        return ESWService.getManualClubs(eventId)
            .then(clubs => res.status(200).json({clubs}))
            .catch(res.customRespError);
    },
    // GET /api/esw/:event/manual-club-teams
    manualClubTeamsList: (req, res) => {
        const eventId = req.params.event,
            clubName = req.query.clubName;

        if(!swUtils.isESWId(eventId)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        return ESWService.getClubTeams({eventId, clubName})
            .then((teams) => res.status(200).json({teams}))
            .catch(res.customRespError);
    },
    // GET /api/esw/:event
    event: function (req, res) {
        const eventId = req.params.event;

        Cache.getResult(req.path, () => ESWService.getEventInfo(eventId), {req})
            .then(event => res.json(event))
            .catch(res.customRespError);
    },
    // GET /api/esw/:event/divisions
    divisions: function (req, res) {
        const event_id = req.params.event;

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        Cache.getResult(req.path, () => Db.query(
            `SELECT 
                e.esw_id "event_id", d.name, 
                d.short_name, d.max_teams, d.division_id, d.gender, 
                e.name "event_name", 
                COUNT(rt.roster_team_id)::INT "teams_count",
                d.has_flow_chart,
                (
                    CASE
                        WHEN (NULLIF(em.file_path, '') IS NOT NULL) THEN concat(em.file_path, '.', em.file_ext)
                        ELSE NULL
                    END
                ) AS flowchart_path,
                EXISTS(
                    SELECT 1
                    FROM division_standing ds
                    WHERE ds.division_id = d.division_id AND ds.rank <> 0
                    LIMIT 1
                ) "is_game_over"
            FROM division d 
            INNER JOIN "event" e 
                ON e.event_id = d.event_id 
            LEFT JOIN roster_team rt 
                ON rt.division_id = d.division_id 
                AND d.published IS TRUE 
                AND rt.deleted IS NULL 
                AND rt.status_entry = 12 
            LEFT JOIN event_media em
                ON em.event_id = e.event_id
                AND em.division_id = d.division_id
                AND em.file_type = 'flowchart'
            WHERE e.esw_id = $1
              AND d.published = true
            GROUP BY d.event_id, d.name, d.short_name, d.created,
            d.modified, d.max_teams, d.division_id, e.name, e.esw_id, flowchart_path
            ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level`,
            [event_id]
        ), {req}).then(function (result) {
            if(result.rowCount === 0) {
                res.send(`No Divisions for Event ${event_id}`)
            } else {
                res.json(result.rows)
            }
        }, function (err) {
            res.send(err)
        })
    },
    // GET /api/esw/:event/divisions/:division/teams
    division_teams: function (req, res) {
        const event_id = req.params.event,
            division_id = parseInt(req.params.division, 10);

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        if(!division_id) {
            return res.validation('Invalid Event Identifier passed');
        }

        /* rt.status_entry = 12 -> Team is Accepted */
        Cache.getResult(req.path, () => Db.query(
            `SELECT 
                rt.roster_team_id, rt.organization_code,
                trim(rt.team_name) "team_name",
                d.short_name, rc.state, rc.club_name
             FROM roster_team rt 
             LEFT JOIN division d 
                ON rt.division_id = d.division_id
             LEFT JOIN roster_club rc 
                ON rc.roster_club_id = rt.roster_club_id
             INNER JOIN "event" e 
                ON e.event_id = rt.event_id
             WHERE e.esw_id = $1
                AND rt.division_id = $2
                AND rt.status_entry = 12
                AND rt.deleted IS NULL
                AND d.published IS TRUE`,
            [event_id, division_id]
        ), {req}).then(function (result) {
            res.json(result.rows)
        }, function (err) {
            res.send(err)
        })
    },
    // GET /api/esw/:event/divisions/:division/standings
    division_standings: async function(req, res) {
        try {
            const event_id = req.params.event,
                division_id = parseInt(req.params.division, 10);

            const withCacheCleaning =  req.query.withCacheCleaning;

            if (withCacheCleaning === 'true') {
                const pattern = `*${event_id}/divisions/${division_id}/standings`;

                await Cache.removeByMask(pattern);
            }

            if(!swUtils.isESWId(event_id)) {
                loggers.errors_log.refError(req, 'Invalid event_id');

                return res.validation('Invalid Event Identifier passed');
            }

            if(!division_id) {
                return res.validation('Invalid Event Identifier passed');
            }

            const result = await Cache.getResult(req.path, () => {
                return Promise.all([
                    __getStandings(event_id, division_id),
                    __getDivisionsSiblings(event_id, division_id)
                ]).then(([teams, { next, prev }]) => ({ teams, next, prev }))
            }, {req});

            res.json(result);
        } catch (err) {
            res.send(err);
        }
    },

    // Redo/remove action - it's incorrect
    pool_standings: function(req, res) {
        var event_id = req.params.event;
        if(!event_id) {
            loggers.errors_log.refError(req, 'Invalid event_id');
            return res.send('No event identifier passed');
        }

        var pool_id = req.param('pool');
        if(!pool_id) return res.send('No pool identifier passed');
        if(!swUtils.isUUID(pool_id)) return res.send('Invalid pool identifier passed');

        var query =
            ' SELECT pb.display_name, ' +
            '   d.short_name as division_short_name, d.name division_name, ' +
            '   pb.pb_stats ' +
            ' FROM poolbrackets pb ' +
            '   LEFT JOIN division d ON d.division_id = pb.division_id  ' +
            ' INNER JOIN "event" e ON e.event_id = pb.event_id ' +
            ' WHERE e.esw_id = $1 ' +
            '   AND pb.uuid = $2 ';

        Cache.getResult(req.path, () => Db.query(query, [event_id, pool_id]), {req})
        .then(function (result) {
            res.json(result.rows[0]);
        }).catch(err => {
            res.send(err);
        })
    },
    // GET /api/esw/:event/clubs
    clubs: function (req, res) {
        const event_id = req.params.event;

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        Cache.getResult(req.path, () => Db.query(
            `SELECT 
                rc.roster_club_id, rc.club_name, rc.state, 
                COUNT(rt.roster_team_id) teams_count 
            FROM roster_club rc 
            INNER JOIN "event" e 
                ON e.event_id = rc.event_id
            INNER JOIN roster_team rt 
                ON rt.roster_club_id = rc.roster_club_id 
            WHERE e.esw_id = $1 
              AND rc.deleted IS NULL 
              AND rt.status_entry = 12 
              AND rt.deleted IS NULL 
            GROUP BY rc.roster_club_id, rc.club_name, rc.state 
            HAVING COUNT(rt.roster_team_id) > 0 
            ORDER BY rc.club_name`,
            [event_id]
        ), {req}).then(function (result) {
            if(result.rowCount === 0) {
                res.send(`No Clubs found for Event ${event_id}`)
            } else {
                res.json(result.rows)
            }
        }, function (err) {
            res.send(err)
        })
    },
    // GET /api/esw/:event/clubs/:club
    club_teams: function (req, res) {
        const eventId = req.params.event,
            clubId = parseInt(req.params.club, 10);

        if(!swUtils.isESWId(eventId)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        if(!clubId) {
            return res.validation('Invalid Club Identifier passed');
        }

        Cache.getResult(req.path, () => ESWService.getClubTeams({eventId, clubId}), {req})
            .then(teams => res.status(200).json(teams))
            .catch(res.customRespError);
    },
    // ???
    teams: function (req, res) {
        var event_id = +req.param('event');
        if(!event_id) {
            loggers.errors_log.refError(req, 'Invalid event_id');
            return res.send('No event identifier passed');
        }

        Cache.getResult(req.path, () => {
            var query =
                ' SELECT rt.* ' +
                ' FROM roster_team rt ' +
                ' WHERE rt.event_id = $1 ' + // show only for event
                '   AND rt.deleted IS NULL ' + // don't show deleted teams
                '   AND rt.status_entry = 12 '; // show only Accepted teams

            return Db.query(query, [event_id]);
        }, {req})
        .then(function (result) {
            res.json(result.rows);
        }).catch(err => {
            res.send(err);
        })
    },
    // GET /api/esw/:event/athletes
    athlete_teams: async function (req, res) {
        let event_id    = req.params.event,
            firstName   = req.query.first,
            lastName    = req.query.last,
            team        = req.query.team,
            state       = req.query.state,
            params      = [],
            query;

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        params.push(event_id);

        query =
            `SELECT 
                rt.roster_team_id, rt.division_id, rt.team_name, 
                ma.first, ma.last, COALESCE(ma.state, mc.state) state, 
                COALESCE(ra.jersey, ma.jersey) "jersey",
                rt.organization_code "team_organization_code"
             FROM "roster_team" rt 
             INNER JOIN "event" e
                ON e.event_id = rt.event_id
             INNER JOIN master_team mt 
                ON mt.master_team_id = rt.master_team_id
             INNER JOIN master_club mc 
                ON mc.master_club_id = mt.master_club_id
             INNER JOIN "roster_athlete" ra
                ON ra.roster_team_id = rt.roster_team_id
                AND ra.deleted IS NULL
                AND ra.deleted_by_user IS NULL
             INNER JOIN master_athlete ma 
                ON ma.master_athlete_id = ra.master_athlete_id
             JOIN state s ON s.state = mc.state   
             WHERE e.esw_id = $1
                AND rt.status_entry = 12
                AND rt.deleted IS NULL`;

        if (firstName) {
            params.push(`%${firstName}%`);
            query += ` AND UPPER(ma.first) LIKE UPPER($${params.length})`;
        }
        if (lastName) {
            params.push(`%${lastName}%`);
            query += ` AND UPPER(ma.last) LIKE UPPER($${params.length})`;
        }
        if (team) {
            params.push(`%${team}%`);
            query += ` AND (UPPER(rt.team_name) LIKE UPPER($${params.length}) 
                    OR UPPER(rt.organization_code) LIKE UPPER($${params.length}))`;
        }
        if (state) {
            params.push(state);
            params.push(`%${state}%`);
            query += ` AND ((s.state = $${params.length-1}) OR UPPER(s.name) LIKE UPPER($${params.length}))`
        }
        query += ' ORDER BY ma.last, ma.first LIMIT 200 ';

        try {
            let {rows: athletes} = await  Db.query(query, params);

            res.status(200).json(athletes);
        } catch (err) {
            res.customRespError(err);
        }
    },
    // GET /api/esw/:event/staff
    staff_teams: function (req, res) {
        var event_id    = req.params.event,
            firstName   = req.query.first,
            lastName    = req.query.last,
            teamName    = req.query.team_name,
            params      = [],
            query;

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        params.push(event_id);

        query =
             `SELECT 
                rt.roster_team_id, rt.division_id, rt.team_name, 
                ms.first, ms.last, ms.state
             FROM roster_team rt 
             INNER JOIN "event" e 
                ON e.event_id = rt.event_id
             INNER JOIN "roster_staff_role" rsr 
                ON rsr.roster_team_id = rt.roster_team_id
                AND rsr.deleted IS NULL
                AND rsr.deleted_by_user IS NULL
             INNER JOIN master_staff ms 
                ON ms.master_staff_id = rsr.master_staff_id
             WHERE e.esw_id = $1
               AND rt.status_entry = 12
               AND rt.deleted IS NULL`;

        if (firstName) {
            params.push(`%${firstName}%`);
            query += ` AND UPPER(ms.first) LIKE UPPER ($${params.length})`;
        }
        if (lastName) {
            params.push(`%${lastName}%`);
            query += ` AND UPPER(ms.last) LIKE UPPER ($${params.length})`;
        }
        if (teamName) {
            params.push(`%${teamName}%`);
            query += ` AND UPPER(rt.team_name) LIKE UPPER ($${params.length})`;
        }
        query += ' ORDER BY ms.last, ms.first  LIMIT 1000  ';

        Db.query(query, params).then(function (result) {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.send(err);
        })
    },
    // GET /api/esw/:event/teams/:team
    team: function (req, res) {
        const event_id = req.params.event,
            team_id = parseInt(req.params.team, 10);

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        if(!team_id) {
            return res.validation('Invalid Team Identifier passed');
        }

        Cache.getResult(req.path, () => Db.query(TEAM_ACTION_SQL, [event_id, team_id]), {req})
        .then(function (result) {
            let matches = _.first(result.rows);
            if(!matches) {
                res.validation('Can not find team with ID:' + team_id);
            } else {
                res.json(matches);
            }
        }).catch(err => {
            res.status(200).send(err);
        })
    },
    // GET /api/esw/:event/courts
    courts: function (req, res) {
        const event_id = req.params.event;

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        Cache.getResult(req.path, () => Db.query(
            `SELECT c.uuid, c.sort_priority, c.name 
             FROM courts c 
             INNER JOIN "event" e 
                ON e.event_id = c.event_id
             WHERE e.esw_id = $1 
                ORDER BY c.sort_priority ASC`, 
            [event_id]
        ), {req}).then(function (result) {
            res.json(result.rows);
        }).catch(err => {
            res.send(err);
        })
    },
    // GET /api/esw/:event/courts/:court
    court_details: function (req, res) {
        const event_id = req.params.event,
            court_id = req.params.court;

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        if(!swUtils.isUUID(court_id)) {
            return res.validation('Invalid Court Identifier passed');
        }

        Cache.getResult(req.path, () => {
            var query =
                ' SELECT r.name r_name, r.short_name r_short_name, r.division_short_name, ' +
                '       m.match_id, m.source, pb.pb_seeds, pb.sort_priority, ' +
                '       m.team1_roster_id, rt1.team_name team1_name,    rt1.organization_code team1_code,  ' +
                '       m.team2_roster_id, rt2.team_name team2_name,    rt2.organization_code team2_code, ' +
                '       m.ref_roster_id,   rt3.team_name ref_team_name, rt3.organization_code ref_team_code, ' +
                '       m.display_name, m.division_short_name, m.division_id, ' +
                '       extract(epoch from m.secs_start)::BIGINT * 1000 date_start, ' +
                '       c.name court_name,' +
                '       m.results ' +
                ' FROM matches m ' +
                ' INNER JOIN "event" e ON e.event_id = m.event_id ' +
                '   LEFT JOIN poolbrackets pb ON pb.uuid = m.pool_bracket_id ' +
                '   LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.team1_roster_id LIMIT 1) rt1 ON true ' +
                '   LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.team2_roster_id LIMIT 1) rt2 ON true ' +
                '   LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.ref_roster_id LIMIT 1) rt3 ON true ' +
                '   LEFT JOIN courts c ON c.uuid = m.court_id ' +
                '   LEFT JOIN rounds r ON pb.round_id = r.uuid ' +
                ' WHERE e.esw_id = $1 AND c.uuid = $2 ' +
                ' ORDER BY r.sort_priority, m.match_number ';

            return Db.query(query, [event_id, court_id]).then(function (result) {
                var matches = result && result.rows;
                if (!matches) throw { validation: 'No matches for event_id ' + event_id };

                matches.forEach(function (match) {
                    if (match.display_name.indexOf('D') === 2) {
                        match.division = match.display_name.substr(3, 1);
                    } else {
                        match.division = null;
                    }

                    if (match.source && match.pb_seeds) {

                        var seeds = [];
                        try {
                            var seedsObj = JSON.parse(match.pb_seeds);
                            _.each(seedsObj, function (seed, key) {
                                seeds[parseInt(key, 10)] = JSON.parse(seed);
                            });
                        } catch (e) {
                            //
                        }

                        try {
                            var source = JSON.parse(match.source);
                        } catch (e) {
                            //
                        }

                        var seed, newPool;
                        seed = newPool = 0;
                        if (source && source['team1'] && source['team1']['type'] && source['team1']['type'] == 5
                            && source['team1']['seed']) {
                            seed = source['team1']['seed'];
                            newPool = seeds[seed];
                            if (newPool) {
                                match.team1_pool_id = newPool.id;
                                match.team1_pool_name = newPool.name;
                            }
                        }

                        if (source && source['team2'] && source['team2']['type'] && source['team2']['type'] == 5
                            && source['team2']['seed']) {
                            seed = source['team2']['seed'];
                            newPool = seeds[seed];
                            if (newPool) {
                                match.team2_pool_id = newPool.id;
                                match.team2_pool_name = newPool.name;
                            }
                        }

                        if (source && source['ref'] && source['ref']['type'] && source['ref']['type'] == 5
                            && source['ref']['seed']) {
                            seed = source['ref']['seed'];
                            newPool = seeds[seed];
                            if (newPool) {
                                match.ref_pool_id = newPool.id;
                                match.ref_pool_name = newPool.name;
                            }
                        }
                    }
                });

                // return res.status(200).json(matches);
                return matches;
            })
        }, {req})
            .then(r => res.json(r))
            .catch(err => err.validation?res.customRespError(err):res.send(err))
    },
    // GET /api/esw/:event/divisions/:division/pools
    division_pools: function (req, res) {
        const event_id = req.params.event,
            division_id = parseInt(req.params.division, 10);

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        if(!division_id) {
            return res.validation('Invalid Division Identifier passed');
        }

        Cache.getResult(req.path, () => {
            var query = `
            SELECT r.uuid r_uuid, r.sort_priority r_sort_priority, pb.sort_priority,
                    rr.sort_priority rr_sort_priority , r.name r_name, r.short_name r_short_name, pb.is_pool,
                    pb.name pb_name, pb.short_name pb_short_name, pb.display_name, pb.team_count, pb.uuid, pb.settings,
                    rr.name rr_name, rr.short_name rr_short_name, r.division_short_name, 
                    NULLIF(pb.pb_stats, '') IS NULL is_empty_pb_stats,

                    (SELECT extract(epoch from m.secs_start)::BIGINT * 1000
                     FROM matches m
                     WHERE m.pool_bracket_id = pb.uuid
                       AND m.event_id = e.event_id
                     ORDER BY m.secs_start ASC, m.match_number ASC
                     LIMIT 1
                    ) date_start,

                    (SELECT c.name
                     FROM matches m
                       INNER JOIN courts c ON c.uuid = m.court_id
                     WHERE m.pool_bracket_id = pb.uuid
                       AND m.event_id = e.event_id
                       AND c.event_id = e.event_id
                     --  AND m.match_number = 1 -- Doug asked to fix that for pools where M1 is not first match in time
                     ORDER BY m.secs_start ASC, m.match_number ASC
                     LIMIT 1
                    ) court_start,

                    (SELECT array_to_json(array_agg(row_to_json(union_teams)))
                     FROM (
                     SELECT rt.team_name opponent_team_name, rt.organization_code opponent_organization_code,
                        rt.roster_team_id opponent_team_id, dst.info, dst.rank,
                        rt.extra->>'show_previously_accepted_bid' show_previously_accepted_bid,
                        NULLIF(CAST(pool_bracket_stats.value ->> 'points_ratio' AS NUMERIC), 0) IS NULL as is_empty_team_pb_stats,
                        pool_bracket_stats.value ->> 'sets_won' as sets_won,
                        pool_bracket_stats.value ->> 'sets_lost' as sets_lost,
                        pool_bracket_stats.value ->> 'matches_won' as matches_won,
                        pool_bracket_stats.value ->> 'matches_lost' as matches_lost,
                        ROUND(CAST(pool_bracket_stats.value ->> 'points_ratio' AS NUMERIC), 3) points_ratio
                     FROM roster_team rt
                     LEFT JOIN matches m ON m.team2_roster_id = rt.roster_team_id
                     LEFT JOIN division_standing dst
                        ON m.event_id = dst.event_id
                        AND m.division_id = dst.division_id AND m.team2_roster_id = dst.team_id
                     LEFT JOIN json_each(COALESCE(NULLIF(pb.pb_stats, ''), '{}')::JSON) pool_bracket_stats
                                 ON (pool_bracket_stats.value ->> 'team_id')::INT = rt.roster_team_id   
                     WHERE m.pool_bracket_id = pb.uuid
                       AND m.team2_roster_id = rt.roster_team_id
                       AND rt.event_id = e.event_id
                       AND m.event_id = e.event_id
                       AND dst.event_id = e.event_id

                     UNION

                     SELECT rt.team_name opponent_team_name, rt.organization_code opponent_organization_code,
                        rt.roster_team_id opponent_team_id, dst.info, dst.rank, 
                        rt.extra->>'show_previously_accepted_bid' show_previously_accepted_bid,
                        NULLIF(CAST(pool_bracket_stats.value ->> 'points_ratio' AS NUMERIC), 0) IS NULL as is_empty_team_pb_stats,
                        pool_bracket_stats.value ->> 'sets_won' as sets_won,
                        pool_bracket_stats.value ->> 'sets_lost' as sets_lost,
                        pool_bracket_stats.value ->> 'matches_won' as matches_won,
                        pool_bracket_stats.value ->> 'matches_lost' as matches_lost,
                        ROUND(CAST(pool_bracket_stats.value ->> 'points_ratio' AS NUMERIC), 3) points_ratio
                     FROM roster_team rt
                     LEFT JOIN matches m ON m.team1_roster_id = rt.roster_team_id
                     LEFT JOIN division_standing dst
                        ON m.event_id = dst.event_id
                        AND m.division_id = dst.division_id AND m.team1_roster_id = dst.team_id
                     LEFT JOIN json_each(COALESCE(NULLIF(pb.pb_stats, ''), '{}')::JSON) pool_bracket_stats
                                 ON (pool_bracket_stats.value ->> 'team_id')::INT = rt.roster_team_id   
                     WHERE m.pool_bracket_id = pb.uuid
                       AND m.team1_roster_id = rt.roster_team_id
                       AND rt.event_id = e.event_id
                       AND m.event_id = e.event_id
                       AND dst.event_id = e.event_id
                       AND rt.event_id = e.event_id


                    ) union_teams
                    ) AS teams

                FROM rounds r
                INNER JOIN "event" e ON e.event_id = r.event_id
                LEFT JOIN poolbrackets pb ON pb.round_id = r.uuid AND pb.event_id = e.event_id
                LEFT JOIN rounds rr ON rr.uuid = pb.group_id AND rr.event_id = e.event_id
                LEFT JOIN division d ON r.division_id = d.division_id AND d.event_id = e.event_id AND d.published IS TRUE
                WHERE e.esw_id = $1 
                  AND r.division_id = $2 
                  AND r.event_id = e.event_id
                ORDER BY r.sort_priority, rr.sort_priority, pb.sort_priority `;

            return Db.query(query, [event_id, division_id]).then(function (result) {
                var teams = result.rows;
                //if(!teams.length) return res.validation('No pools for event_id ' + event_id + ' and division_id ' + division_id);
                return teams;
            })
        }, { req })
            .then(r => res.json(r))
            .catch(err => res.serverError(err))
    },
    // GET /api/esw/:event/matches/:match
    match: function(req, res) {
        const event_id = req.params.event,
            match_id = req.params.match;

        if(!swUtils.isESWId(event_id)) {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier passed');
        }

        if(!match_id) {
            return res.validation('No match identifier passed');
        }

        if(!swUtils.isUUID(match_id)) {
            loggers.errors_log.refError(req, 'Incorrect match id');

            return res.validation('Incorrect match id');
        }

        Cache.getResult(req.path, () => {
            var query =
                ' SELECT m.match_id, m.source, m.pool_bracket_id, pb.is_pool, pb.pb_seeds, pb.pb_finishes, ' +
                '       m.division_id, pb.division_short_name, ' +
                '       m.team1_roster_id, rt1.team_name team1_name,    rt1.organization_code team1_code,  ' +
                '       m.team2_roster_id, rt2.team_name team2_name,    rt2.organization_code team2_code, ' +
                '       m.ref_roster_id,   rt3.team_name ref_team_name, rt3.organization_code ref_team_code, ' +
                '       m.display_name, m.match_number, pb.name pb_name, r.name round_name, m.division_short_name, ' +
                '       m.division_id, c.name court_name, ' +
                '       extract(epoch from m.secs_start)::BIGINT * 1000 date_start, ' +
                '       extract(epoch from m.secs_end)::BIGINT * 1000 date_end, ' +
                '       m.results ' +
                ' FROM matches m ' +
                '   INNER JOIN "event" e ON e.event_id = m.event_id ' +
                '   LEFT JOIN poolbrackets pb ON pb.uuid = m.pool_bracket_id ' +
                '   LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.team1_roster_id LIMIT 1) rt1 ON true ' +
                '   LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.team2_roster_id LIMIT 1) rt2 ON true ' +
                '   LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.ref_roster_id LIMIT 1) rt3 ON true ' +
                '   LEFT JOIN courts c ON c.uuid = m.court_id ' +
                '   LEFT JOIN rounds r ON r.uuid = m.round_id ' +
                ' WHERE e.esw_id = $1 ' +
                '   AND m.match_id = $2 ';

            return Db.query(query, [event_id, match_id]).then(function (result) {
                var match = _.first(result.rows);
                if(!match) {
                    throw { validation : `Match does not exist ${match_id}` };
                }

                if (match.source && match.pb_seeds) {

                    var seeds = [];
                    try {
                        var seedsObj = JSON.parse(match.pb_seeds);
                        _.each(seedsObj, function (seed, key) {
                            seeds[parseInt(key, 10)] = JSON.parse(seed);
                        });
                    } catch (e) {
                        //
                    }

                    try {
                        var source = JSON.parse(match.source);
                    } catch (e) {
                        //
                    }

                    var seed, newPool;
                    seed = newPool = 0;
                    if (source && source['team1'] && source['team1']['type'] && source['team1']['type'] == 5
                        && source['team1']['seed']) {
                        seed = source['team1']['seed'];
                        newPool = seeds[seed];
                        if (newPool) {
                            match.team1_pool_id = newPool.id;
                            match.team1_pool_name = newPool.name;
                        }
                    }

                    if (source && source['team2'] && source['team2']['type'] && source['team2']['type'] == 5
                        && source['team2']['seed']) {
                        seed = source['team2']['seed'];
                        newPool = seeds[seed];
                        if (newPool) {
                            match.team2_pool_id = newPool.id;
                            match.team2_pool_name = newPool.name;
                        }
                    }

                    if (source && source['ref'] && source['ref']['type'] && source['ref']['type'] == 5
                        && source['ref']['seed']) {
                        seed = source['ref']['seed'];
                        newPool = seeds[seed];
                        if (newPool) {
                            match.ref_pool_id = newPool.id;
                            match.ref_pool_name = newPool.name;
                        }
                    }

                }

                // return res.status(200).json(match);
                return match;
            })
        }, {req})
            .then(r => res.json(r))
            .catch(err => res.customRespError(err))
    },
    // GET /api/esw/:event/pools/:pool
    pool: function(req, res) {
        const eventId = req.params.event,
            poolId = req.params.pool;

        Cache.getResult(req.path, () => ESWService.poolsService.getPoolInfo(eventId, poolId), {req})
            .then(poolInfo => res.json(poolInfo))
            .catch(res.customRespError);
    },


    pool_standings__: function (req, res) {
        var event_id = +req.param('event');
        if(!event_id) {
            loggers.errors_log.refError(req, 'Invalid event_id');
            return res.status(400).json({error: 'Event is not specified.'});
        }
        var pool_id = req.param('id');
        if(!swUtils.isUUID(pool_id)) {
            loggers.errors_log.refError(req, 'Invalid pool id');
            return res.status(400).json({error: 'Invalid pool id'});
        }

        var stats, standings;

        var query =
            ' SELECT pb.pb_stats, ' +
            '       pb.display_name ' +
            ' FROM poolbrackets pb ' +
            ' WHERE pb.event_id = $1 ' +
            '   AND pb.uuid = $2 ';

        Cache.getResult(req.path, () => Db.query(query, [event_id, pool_id])
            .then(function (result) {

                // pb_stats = {
                // "1st":{"team_id":"3622","name":"Carolina Storm 18 Blue","matches_won":1,"matches_lost":1,"sets_won":3,"sets_lost":2,"points_won":75,"points_lost":31,"matches_pct":50.0,"sets_pct":60.0,"points_ratio":2.419354915618896,"rank":"2"},
                // "2nd":{"team_id":"2602","name":"WGVA 18-1","matches_won":2,"matches_lost":0,"sets_won":4,"sets_lost":0,"points_won":100,"points_lost":0,"matches_pct":100.0,"sets_pct":100.0,"points_ratio":1.0,"rank":"4"},
                // "3rd":{"team_id":"3424","name":"ATLBoom 18 Orange","matches_won":0,"matches_lost":3,"sets_won":0,"sets_lost":6,"points_won":0,"points_lost":63,"matches_pct":0.0,"sets_pct":0.0,"points_ratio":0.0,"rank":"1"},"4th":{"team_id":"645","name":"Aftershock 181","matches_won":2,"matches_lost":1,"sets_won":4,"sets_lost":3,"points_won":90,"points_lost":59,"matches_pct":66.666664123535156,"sets_pct":57.142856597900391,"points_ratio":1.525423765182495,"rank":"3"}}
                var pool;
                if (result.rows && result.rows[0]) {
                    pool = result.rows[0];
                    if (pool.pb_stats) {

                        try {
                            stats = JSON.parse(pool.pb_stats);
                        } catch(e) {
                            //
                        }
                        if (stats.length > 0) {
                            _.each(stats, function(stat, key){
                                standings.push(stat);
                            });
                        }
                    }
                }


                return {pool: pool, standings: standings};
            }),
            {
                req,
            }
        )
            .then(r => res.json(r))
            .catch(err => res.serverError(err))
    },

    // GET /api/esw/:event/courts_matches/:day/hour/:hour/hours/:hours/:division
    courts_matches: function (req, res) {
        const eventId            = req.params.event,
            day                 = req.params.day,
            hour                = req.params.hour,
            hoursCount  = parseInt(req.params.hours, 10),
            divisionId         = Number(req.params.division) || null;

        const params = {
            eventId,
            day,
            hour,
            hoursCount,
            divisionId,
        }

        Cache.getResult(req.path, () => ESWService.matchesService.getCourtsMatches(params), {req})
            .then(courtsMatches => res.json(courtsMatches))
            .catch(res.customRespError)
    },

    // GET /api/esw/:event/brackets/:bracket/matches
    bracket_matches: async function(req, res) {
        const eventId = req.params.event,
            bracketId = req.params.bracket;

        Cache.getResult(req.path, () => ESWService.getBracketMatches(eventId, bracketId), {req})
            .then(bracketMatches => res.status(200).json(bracketMatches))
            .catch(err => res.status(200).send(err))
    },

    // GET /api/esw/club/event/:event/divisions_list
    roster_for_event: function (req, res) {
        const event_id = req.params.event;
        const event_id_num = parseInt(req.params.event, 10);
        let eventCheckBlock;

        if(swUtils.isESWId(event_id)) {
            eventCheckBlock = 'e.esw_id'
        } else if(event_id_num) {
            eventCheckBlock = 'e.event_id'
        } else {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier');
        }

        const query =
            `select 
                d.name "division_name", d.gender, d.division_id, FALSE "checked",
                coalesce(nullif(d.max_teams, 0), d.max_teams) "maximum", (
                    case
                        when (e.public_teams_visibility::text = 'hidden') then '[]'::json
                        else (
                            select coalesce(array_to_json(array_agg(row_to_json(t))), '[]'::json)
                            from (
                                select  rt.gender, rt.team_name, rc.club_name, (rt.status_entry = ${teamsConstants.ENTRY_STATUSES.ACCEPTED}) "accepted",
                                    (CASE
                                        WHEN e.show_team_entry_status THEN
                                            CASE rt.status_entry
                                                WHEN ${teamsConstants.ENTRY_STATUSES.ACCEPTED} THEN 'accepted'
                                                WHEN ${teamsConstants.ENTRY_STATUSES.PENDING} THEN 'pending'
                                                WHEN ${teamsConstants.ENTRY_STATUSES.WAITLIST} THEN 'waitlisted'
                                                ELSE NULL
                                            END
                                        ELSE NULL
                                    END) "accepted_status_icon"
                                from roster_team rt
                                left join roster_club rc
                                    on rc.roster_club_id = rt.roster_club_id
                                where rt.division_id = d.division_id  
                                    and rt.event_id = d.event_id  
                                    and rt.deleted is null 
                                    and rt.status_entry = any (  
                                        (case  
                                            when e.public_teams_visibility::text = 'all' 
                                            then '{${teamsConstants.ENTRY_STATUSES.ACCEPTED}, ${teamsConstants.ENTRY_STATUSES.PENDING}, ${teamsConstants.ENTRY_STATUSES.WAITLIST}}'  
                                            else '{${teamsConstants.ENTRY_STATUSES.ACCEPTED}}'   
                                        end)::integer[] 
                                    ) 
                                order by rt.team_name asc, rt.status_entry asc 
                            ) "t"
                        )
                    end
                ) "teams", (  
                    select (
                        CASE
                            WHEN e.show_number_of_teams_for_public IS TRUE
                            THEN count(a_rt.roster_team_id)
                            ELSE 0
                        END
                    )  
                    from roster_team a_rt 
                    where a_rt.division_id = d.division_id 
                        and a_rt.event_id = d.event_id 
                        and a_rt.deleted is null 
                        and a_rt.status_entry = ${teamsConstants.ENTRY_STATUSES.ACCEPTED} 
                 )::int "accepted_teams_count", (  
                    select (
                        CASE
                            WHEN e.show_number_of_teams_for_public IS TRUE
                            THEN count(e_rt.roster_team_id)
                            ELSE 0
                        END
                    )  
                    from roster_team e_rt 
                    where e_rt.division_id = d.division_id 
                        and e_rt.event_id = d.event_id 
                        and e_rt.deleted is null 
                        and e_rt.status_entry in (
                            ${teamsConstants.ENTRY_STATUSES.ACCEPTED},
                            ${teamsConstants.ENTRY_STATUSES.PENDING},
                            ${teamsConstants.ENTRY_STATUSES.WAITLIST}
                        ) 
                 )::int "entered_teams_count"
            from "event" e
            left join "division" d
                  on d.event_id = e.event_id
            where ${eventCheckBlock} = $1
                  and e.live_to_public = true
                  and d.published = true
            group by d.division_id, e.event_id
            ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level`;

        Cache.getResult(req.path, () => Db.query(query, [event_id]), {req}).then(function (result) {
            res.status(200).json({ divisions: result.rows });
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // GET /api/esw/club/event/:event/division/:division/teams_list
    teams_for_event: function(req, res) {
        const event_id = req.params.event,
            event_id_num = parseInt(req.params.event, 10),
            division_id = parseInt(req.params.division, 10);

        let eventCheckBlock;
        if(swUtils.isESWId(event_id)) {
            eventCheckBlock = 'e.esw_id'
        } else if(event_id_num) {
            eventCheckBlock = 'e.event_id'
        } else {
            loggers.errors_log.refError(req, 'Invalid event_id');

            return res.validation('Invalid Event Identifier');
        }

        if(!division_id) {
            return res.validation('Invalid Division Identifier');
        }

        Cache.getResult(req.path, () => Db.query(
            `SELECT rt.gender, rt.team_name, rc.club_name
             FROM roster_team rt 
             INNER JOIN "event" e
                 ON e.event_id = rt.event_id
             LEFT JOIN roster_club rc 
                 ON rt.roster_club_id = rc.roster_club_id 
             WHERE ${eventCheckBlock} = $1
                 AND rt.division_id = $2 
                 AND rt.deleted IS NULL 
                 AND rt.status_entry = 12 
             ORDER BY rt.team_name ASC`, 
            [event_id, division_id]
        ), {req}).then(function (result) {
            res.json({ teams: result.rows });
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // GET /api/esw/club/event/:event/info
    get_info: function(req, res) {
        const event_id        = req.params.event,
            event_id_num    = parseInt(event_id, 10);

        if(!event_id) {
            return res.validation('Empty Event identifier passed');
        }

        Cache.getResult(req.path, () => {
            var query = squel.select()
                .from('event', 'e')
                .field('e.event_id')
                .field('e.mincount_enter')
                .field('to_char(e.date_start, \'MM/DD/YYYY\') date_start')
                .field('to_char(e.date_end, \'MM/DD/YYYY\') date_end')
                .field('to_char(e.date_reg_open, \'MM/DD/YYYY\') date_reg_open')
                .field('to_char(e.date_reg_close, \'MM/DD/YYYY\') date_reg_close')
                .field('e.email')
                .field('e.mincount_accept')
                .field('e.has_coed_teams')
                .field('e.has_female_teams')
                .field('e.has_male_teams')
                .field('e.has_rosters')
                .field('e.long_name')
                .field('e.name')
                .field('e.reg_fee')
                .field('e.roster_deadline')
                .field('e.sport_id')
                .field('e.website')
                .field('e.sport_variation_id')
                .field('e.sport_sanctioning_id')
                .field('e.region')
                .field('e.has_status_housing')
                .field('e.payment_address')
                .field('e.payment_name')
                .field('e.payment_city')
                .field('e.payment_state')
                .field('e.payment_zip')
                .field('e.payment_country')
                .field('e.hosting_org_address')
                .field('e.hosting_org_city')
                .field('e.hosting_org_name')
                .field('e.hosting_org_phone')
                .field('e.hosting_org_state')
                .field('e.hosting_org_zip')
                .field('e.housing_company_id')
                .field('e.custom_housing_company')
                .field('e.event_notes')
                .field('e.published')
                .field('e.live_to_public')
                .field('rc.created', 'club_entered')
                .field('e.public_teams_visibility')
                .field('e.registration_method')
                .field(
                    `(CASE WHEN (e.registration_method = 'doubles' 
                                   AND e.published = TRUE
                                   AND e.teams_use_clubs_module IS TRUE 
                                   AND (COALESCE(MAX(d.date_reg_close), e.date_reg_close) 
                                                                    >= (NOW() AT TIME ZONE e.timezone)))
                               THEN TRUE
                               ELSE FALSE
                    END)`, 'doubles_reg_available')
                .field('e.social_links')
                .field('e.allow_teams_registration')
                .field('(case when ( \
                        e.tickets_published  \
                        and e.tickets_purchase_date_start <= (now() AT TIME ZONE e.timezone)  \
                        and e.tickets_purchase_date_end > (now() AT TIME ZONE e.timezone) \
                        ) then e.event_tickets_code  \
                        else null  \
                    end)', 'tickets_code')
                .field('to_char(e.tickets_purchase_date_start, \'MM/DD/YYYY\') tickets_purchase_date_start')
                .field('e.tickets_published')
                .left_join('roster_club', 'rc', 'rc.event_id = e.event_id ')
                .left_join(`division`, `d`,
                    `d.event_id = e.event_id AND d.closed IS NULL AND d.published IS TRUE`)
                .where('e.live_to_public IS TRUE')
                .field(
                    squel.select()
                        .field('array_to_json(array_agg(fees.reg_fee))')
                        .from(squel.select()
                            .field('dv.reg_fee')
                            .distinct()
                            .from('division', 'dv')
                            .where('dv.event_id = ?', event_id)
                            .where('dv.reg_fee <> 0')
                            .where('dv.closed IS NULL')
                            .where('dv.published IS TRUE'), 'fees')
                    , 'division_fees'
                )
                .group('e.event_id, rc.created');

            if (swUtils.isESWId(event_id)) {
                query.where('e.esw_id = ? ', event_id)
            } else if (event_id_num) {
                query.where('e.event_id = ?', event_id_num)
            } else {
                throw { validation: 'Invalid Event Identifier passed' };
            }

            if (req.query.loc) {
                query
                    .left_join('event_location', 'el', 'el.event_id = e.event_id AND el.number = 1')
                    .field('el.state')
                    .field('el.city')
                    .field('el.zip')
                    .field('el.address')
                    .field('el.name', 'location_name')
                    .field('el.short_name')
                    .group('el.event_location_id')
            }

            return Db.query(query)
                .then(function (result) {
                    return {
                        event: EventUtils.formatEventInfoFees(result.rows[0])
                    };
                });
        }, {req})
            .then(r => res.json(r))
            .catch(res.customRespError)
    },

    // GET /api/esw/:event/prevqual
    getPrevqualTeamsList: function (req, res) {
        const eventID = Number(req.params.event);

        Cache.getResult(req.path, () => ESWService.getPrevQualTeams(eventID), {req})
            .then(teams => res.json({teams}))
            .catch(res.customRespError)
    },
};

function __getStandings (eventId, divisionId) {
    return Db.query(
        `SELECT o.heading_group, ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(o))) "teams"
          FROM (
            SELECT
                 CAST(ds.info->>'seed_current' AS INTEGER) "seed_current",
                 rt.roster_team_id,
                 rt.team_name,
                 rt.organization_code,
                 rt.extra->>'show_accepted_bid' "show_accepted_bid",
                 rt.extra->>'show_previously_accepted_bid' "show_previously_accepted_bid",
                 rc.club_name,
                 rc.city,
                 rc.state,
                 d.short_name "division_short_name",
                 d.name "division_name",
                 d.level "division_level",
                 ds.matches_won,
                 ds.matches_lost,
                 ds.sets_won,
                 ds.sets_lost,
                 ds.points_won,
                 ds.points_lost,
                 ds.sets_pct,
                 ds.points_ratio,
                 ds.rank,
                 ds.info,
                 COALESCE(NULLIF(ds.info->>'heading', ''), 'None') "heading_group",
                 CAST(ds.info->>'heading_sort_priority' AS INTEGER) "heading_sorting_group"
            FROM roster_team rt
            inner join "event" e
                on e.event_id = rt.event_id
            LEFT JOIN roster_club rc
                ON rc.roster_club_id = rt.roster_club_id
            LEFT JOIN division d
                ON rt.division_id = d.division_id
            LEFT JOIN division_standing ds
                ON rt.division_id = ds.division_id
                AND ds.team_id = rt.roster_team_id
            WHERE e.esw_id = $1
                AND rt.division_id = $2
                AND rt.deleted IS NULL
                AND rt.status_entry = 12
                AND d.published IS TRUE
            ORDER BY CAST(ds.info->>'heading_sort_priority' AS INTEGER), ds.info->>'heading', ds.rank,
                CASE WHEN e.hide_seeds IS TRUE THEN rt.team_name END,
                CASE WHEN e.hide_seeds IS NOT TRUE THEN CAST(ds.info->>'seed_current' AS INTEGER) END,
                ds.sets_pct DESC, ds.points_ratio DESC
          ) o
        GROUP BY o.heading_group, o.heading_sorting_group
        ORDER BY o.heading_sorting_group`,
        [eventId, divisionId]
    ).then(function (result) {
        return result.rows;
    }).then(function (divisions) {
        let div_teams = {};

        divisions.forEach(division => {
            let group = division.heading_group;

            if(!_.isArray(div_teams[group])) {
                div_teams[group] = [];
            }

            div_teams[group] = div_teams[group].concat(division.teams)
        });

        return div_teams;
    })
}

function __getDivisionsSiblings (eventId, divisionId) {
    return Db.query(
        `WITH divisions_list AS (
            SELECT *, 
                ROW_TO_JSON(LAG(d, 1) OVER()) "prev", 
                ROW_TO_JSON(LEAD(d, 1) OVER()) "next"
            FROM (
                 SELECT 
                     d.division_id "id", d.short_name "name"
                 FROM "division" d
                 INNER JOIN "event" e
                     ON e.event_id = d.event_id
                 WHERE e.esw_id = $1
                 ORDER BY d.name
            ) d
        )
        SELECT "prev", "next" FROM "divisions_list" WHERE "id" = $2`,
        [eventId, divisionId]
    ).then(function (result) {
        var row = _.first(result.rows) || {}
        return row;
    })
}

var TEAM_ACTION_SQL = 
`SELECT rt.roster_team_id, rt.team_name,
       rt.matches_won, rt.matches_lost, rt.manual_club_name,
       rt.sets_won, rt.sets_lost, rt.organization_code,
       rt.points_won, rt.points_lost, rt.roster_club_id, rc.club_name, rc.state,
 (SELECT array_to_json(array_agg(row_to_json(union_matches))) 
   FROM ( 
       SELECT 'team1' match_type,  m.match_id, 
               extract(epoch from m.secs_finished)::BIGINT as unix_finished, 
               m.results, m.display_name, 
               to_char(m.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,  
               extract(epoch from m.secs_start)::BIGINT * 1000 date_start,  
               m.division_id, m.division_short_name, c.name court_name, pb.display_name pool_name, m.pool_bracket_id, 
               pb.is_pool, rt2.team_name opponent_team_name, rt2.organization_code opponent_organization_code,  
               pb.name pb_name, r.name round_name, m.footnote_play, m.footnote_team1, m.footnote_team2
           FROM matches m 
           INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid AND pb.event_id = e.event_id  
           LEFT JOIN courts c ON c.uuid = m.court_id AND c.event_id = e.event_id
           LEFT JOIN LATERAL (SELECT _rt.team_name, _rt.organization_code FROM roster_team _rt WHERE m.team2_roster_id = _rt.roster_team_id AND _rt.status_entry = 12 AND _rt.event_id = e.event_id LIMIT 1) rt2 ON TRUE
           LEFT JOIN rounds r ON r.uuid = m.round_id AND r.event_id = e.event_id
           WHERE m.team1_roster_id = rt.roster_team_id 
             AND m.secs_finished IS NULL 
             AND m.event_id = e.event_id
             
            UNION
             
           SELECT 'team2' match_type, m.match_id, 
               extract(epoch from m.secs_finished)::BIGINT as unix_finished, 
               m.results, m.display_name,  
               to_char(m.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,  
               extract(epoch from m.secs_start)::BIGINT * 1000 date_start,  
               m.division_id, m.division_short_name, c.name court_name, pb.display_name pool_name, m.pool_bracket_id, 
               pb.is_pool, rt2.team_name opponent_team_name, rt2.organization_code opponent_organization_code,  
               pb.name pb_name, r.name round_name, m.footnote_play, m.footnote_team1, m.footnote_team2
           FROM matches m 
           INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid AND pb.event_id = e.event_id
           LEFT JOIN courts c ON c.uuid = m.court_id AND c.event_id = e.event_id
           LEFT JOIN LATERAL (SELECT _rt.team_name, _rt.organization_code FROM roster_team _rt WHERE m.team1_roster_id = _rt.roster_team_id AND _rt.status_entry = 12 AND _rt.event_id = e.event_id LIMIT 1) rt2 ON TRUE
           LEFT JOIN rounds r ON r.uuid = m.round_id AND r.event_id = e.event_id
           WHERE m.team2_roster_id = rt.roster_team_id  
             AND m.secs_finished IS NULL
             AND m.event_id = e.event_id
              
           UNION
            
           SELECT 'ref' match_type, m.match_id, 
               extract(epoch from m.secs_finished)::BIGINT as unix_finished, 
               m.results, m.display_name,  
               to_char(m.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,  
               extract(epoch from m.secs_start)::BIGINT * 1000 date_start,  
               m.division_id, m.division_short_name, c.name court_name, pb.display_name pool_name, m.pool_bracket_id, 
               pb.is_pool, rt2.team_name opponent_team_name, rt2.organization_code opponent_organization_code,  
               pb.name pb_name, r.name round_name, m.footnote_play, m.footnote_team1, m.footnote_team2
           FROM matches m 
           INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid AND pb.event_id = e.event_id
           LEFT JOIN courts c ON c.uuid = m.court_id AND c.event_id = e.event_id
           LEFT JOIN LATERAL (SELECT _rt.team_name, _rt.organization_code FROM roster_team _rt WHERE m.team1_roster_id = _rt.roster_team_id AND _rt.status_entry = 12 AND _rt.event_id = e.event_id LIMIT 1)  rt2 ON TRUE
           LEFT JOIN rounds r ON r.uuid = m.round_id AND r.event_id = e.event_id
           WHERE m.ref_roster_id = rt.roster_team_id 
             AND m.secs_finished IS NULL 
             AND m.event_id = e.event_id
             
           ORDER BY date_start ASC  
   ) union_matches 
 ) AS upcoming, 
 (SELECT array_to_json(array_agg(row_to_json(pool_matches))) 
   FROM ( 
          SELECT pb.uuid, pb.is_pool, pb.name pb_name, r.name round_name, pb.pb_stats, r.sort_priority, 
                  pb.sort_priority, 
              (SELECT array_to_json(array_agg(row_to_json(union_matches)))::text 
                FROM ( 
                   SELECT 'team1' match_type, m2.match_id, 
                    extract(epoch from m2.secs_finished)::BIGINT as unix_finished, 
                    m2.results, m2.display_name, 
                    to_char(m2.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,  
                    extract(epoch from m2.secs_start)::BIGINT * 1000 date_start,  
                    m2.division_id, m2.division_short_name, m2.pool_bracket_id, 
                    rt2.team_name opponent_team_name, rt2.organization_code opponent_organization_code, 
                    rt2.roster_team_id opponent_team_id  
                    FROM matches m2 
                    INNER JOIN poolbrackets pb2 ON m2.pool_bracket_id = pb2.uuid AND pb2.event_id = e.event_id  
                    LEFT JOIN LATERAL (SELECT _rt.roster_team_id, _rt.team_name, _rt.organization_code FROM roster_team _rt WHERE m2.team2_roster_id = _rt.roster_team_id AND _rt.status_entry = 12 AND _rt.event_id = e.event_id LIMIT 1) rt2 ON TRUE 
                    WHERE m2.pool_bracket_id = pb.uuid 
                      AND m2.event_id = e.event_id
                      AND m2.team1_roster_id = rt.roster_team_id
                      
                       UNION
                        
                    SELECT 'team2' match_type, m2.match_id, 
                    extract(epoch from m2.secs_finished)::BIGINT as unix_finished, 
                    m2.results, m2.display_name, 
                    to_char(m2.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,  
                    extract(epoch from m2.secs_start)::BIGINT * 1000 date_start,  
                    m2.division_id, m2.division_short_name, m2.pool_bracket_id, 
                    rt2.team_name opponent_team_name, rt2.organization_code opponent_organization_code, 
                    rt2.roster_team_id opponent_team_id  
                    FROM matches m2 
                    INNER JOIN poolbrackets pb2 ON m2.pool_bracket_id = pb2.uuid AND pb2.event_id = e.event_id
                    LEFT JOIN LATERAL (SELECT _rt.roster_team_id, _rt.team_name, _rt.organization_code FROM roster_team _rt WHERE m2.team1_roster_id = _rt.roster_team_id AND _rt.status_entry = 12 AND _rt.event_id = e.event_id LIMIT 1) rt2 ON TRUE 
                    WHERE m2.pool_bracket_id = pb.uuid
                      AND m2.event_id = e.event_id
                      AND m2.team2_roster_id = rt.roster_team_id
                      
                    ORDER BY date_start ASC  
               ) union_matches 
               ) AS matches  
           FROM matches m 
             INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid AND pb.event_id = e.event_id  
             LEFT JOIN rounds r ON r.uuid = m.round_id AND r.event_id = e.event_id
           WHERE (m.team1_roster_id = rt.roster_team_id OR m.team2_roster_id = rt.roster_team_id)
             AND m.event_id = e.event_id
             AND m.secs_finished IS NOT NULL 
           GROUP BY r.name, pb.name, pb.is_pool, pb.uuid, r.sort_priority, pb.sort_priority 
           ORDER BY r.sort_priority, pb.sort_priority ASC  
   ) pool_matches 
) "results",  
 (SELECT coalesce(array_to_json(array_agg(row_to_json(teamathletes))), '[]'::json) 
   FROM (
       SELECT ma.first, ma.last, 
            COALESCE(ra.jersey, ma.jersey) "uniform",
            COALESCE(ra.aau_jersey, ma.aau_jersey) "aau_uniform",
            COALESCE(sp.short_name, spm.short_name) "short_position", 
            ma.gradyear, ma.height 
       FROM master_athlete ma 
       INNER JOIN roster_athlete ra 
            ON ra.master_athlete_id = ma.master_athlete_id
            AND ra.event_id = rt.event_id
            AND ra.deleted IS NULL
            AND ra.deleted_by_user IS NULL
       LEFT JOIN sport_position sp 
            ON sp.sport_position_id = ra.sport_position_id 
        LEFT JOIN "sport_position" spm 
            ON spm.sport_position_id = ma.sport_position_id
       WHERE ra.roster_team_id = rt.roster_team_id
         AND (ra.as_staff = 0 OR ra.as_staff IS NULL)
       ORDER BY COALESCE(ra.jersey, ma.jersey) ASC
 ) teamathletes) "athletes", 
 (SELECT coalesce(array_to_json(array_agg(row_to_json(teamstaff))), '[]'::json)
   FROM (
       SELECT ms.first, ms.last, COALESCE(r.name, rm.name) "role_name", r.sort_order
       FROM master_staff ms 
       INNER JOIN roster_staff_role rsr 
            ON rsr.master_staff_id = ms.master_staff_id
            AND rsr.roster_team_id = rt.roster_team_id
            AND rsr.deleted IS NULL
            AND rsr.deleted_by_user IS NULL
        LEFT JOIN "master_staff_role" msr 
            ON msr.master_staff_id = rsr.master_staff_id
            AND msr.master_team_id = rsr.master_team_id
        LEFT JOIN "role" r 
            ON r.role_id = rsr.role_id 
        LEFT JOIN "role" rm 
            ON rm.role_id = msr.role_id

       UNION 

       SELECT ma.first, ma.last, 
            'Staff' AS role_name,
            NULL::INTEGER as sort_order
       FROM master_athlete ma 
       INNER JOIN roster_athlete ra 
            ON ra.master_athlete_id = ma.master_athlete_id
            AND ra.event_id = rt.event_id
            AND ra.deleted IS NULL
            AND ra.deleted_by_user IS NULL
       WHERE ra.roster_team_id = rt.roster_team_id
         AND ra.as_staff > 0
         ORDER BY sort_order, role_name
 ) teamstaff) "staff",
 (SELECT m.finishes FROM matches m
    LEFT JOIN poolbrackets pb
        ON pb.uuid = m.pool_bracket_id
    WHERE (team2_roster_id = $2 OR team1_roster_id = $2 OR ref_roster_id = $2)
        AND m.event_id = e.event_id
        AND m.secs_finished IS NULL
        AND pb.is_pool = 0
        ORDER BY m.secs_start DESC
        LIMIT 1
 ) "bracket_finishes"
 FROM roster_team rt 
 INNER JOIN "event" e 
    ON e.event_id = rt.event_id 
 LEFT JOIN roster_club rc 
    ON rt.roster_club_id = rc.roster_club_id
 WHERE e.esw_id = $1 
   AND rt.status_entry = 12 
   AND rt.roster_team_id = $2
   AND rt.event_id = e.event_id
   AND rt.deleted IS NULL`;

function __filteringCampsFromTickets(data, isCampsNeededToReturn) {
    let filtered= [];
    data.forEach( e => {
        if( e.ticket_camps_registration === isCampsNeededToReturn ) {
            let clone= _.omit(e, "ticket_camps_registration");
            filtered.push(clone);
        }
    });
    return filtered;
};
