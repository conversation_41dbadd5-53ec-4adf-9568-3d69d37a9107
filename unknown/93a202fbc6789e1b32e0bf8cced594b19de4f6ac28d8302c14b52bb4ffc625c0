const teamsConstants = require('../../../../../constants/teams');

const MAX_ATHLETES_PER_PAGE = 100;

module.exports = {
    friendlyName: 'Get teams',
    description: 'Get teams',
    
    inputs: {
        
        event_id: {
            type: 'ref',
            description: 'Event ID'
        },
        athleteFirstName: {
            type: 'string',
            example: '<PERSON>',
            description: 'Athlete first name'
        },
        athleteLastName: {
            type: 'string',
            example: '<PERSON>',
            description: 'Athlete last name'
        },
        teamClubName: {
            type: 'string',
            description: 'Club name or Team name'
        },
        state: {
            type: 'string',
            example: 'VA',
            description: 'Club state'
        },
        teamCode: {
            type: 'string',
            example: 'G16TKIWI99FL',
            description: 'Team code'
        },
        page: {
            type: 'ref',
            description: 'Page number'
        },
        perPage: {
            type: 'ref',
            description: 'Items count in page'
        }
    },
    
    exits: {
        success: {
            statusCode: 200
        },
        resourceNotFound: {
            statusCode: 404
        },
        validationError: {
            statusCode: 400
        },
        serverError: {
            statusCode: 500
        }
    },
    
    fn: async function(inputs, exits) {
        const eventId = parseInt(inputs.event_id, 10);
        const athleteFirstName = inputs.athleteFirstName;
        const athleteLastName = inputs.athleteLastName;
        const teamClubName = inputs.teamClubName;
        const state = inputs.state;
        const teamCode = inputs.teamCode;
        let page = parseInt(inputs.page, 10);
        let perPage = parseInt(inputs.perPage, 10);
    
        if(!page || !perPage || perPage > MAX_ATHLETES_PER_PAGE) [page, perPage] = [1, MAX_ATHLETES_PER_PAGE];
        
        const paramsValidationError = await sails.helpers.acs.validateProperties.with({
            properties: {
                event_id: eventId
            }
        });
    
        if(!_.isEmpty(paramsValidationError)) {
            throw {
                validationError: {
                    error: paramsValidationError
                }
            };
        }
    
        const dataExistenceError = await sails.helpers.acs.checkEventTeamByCode.with({ eventId });
    
        if (dataExistenceError) {
            throw {
                resourceNotFound: {
                    error: {
                        messages: [dataExistenceError],
                        properties: []
                    }
                }
            };
        }
        
        try {
            const teams = await getTeams({
                eventId,
                firstName: athleteFirstName,
                lastName: athleteLastName,
                teamClubName,
                teamCode,
                state,
                page,
                perPage
            });

            exits.success(teams || []);
        } catch(err) {
            loggers.errors_log.error(err);

            throw {
                serverError: {
                    error: {
                        messages: ['Server internal error'],
                        properties: []
                    }
                }
            };
        }
    }
};

function getTeams ({ eventId, firstName, lastName, teamClubName, teamCode, state, page, perPage }) {
    const query = knex('roster_team AS rt')
        .select(
            'rt.roster_team_id', 'rt.team_name', 'rt.organization_code', 'rc.state', 'rc.club_name',
            knex.raw(`
                coalesce(array_to_json(array_agg(json_build_object(
                   'first', ma.first,
                   'last', ma.last
               ) ORDER BY ma.last, ma.first) FILTER ( WHERE ma.master_athlete_id IS NOT NULL )), '[]'::JSON) AS athletes
            `)
        )
        .join('event AS e', 'e.event_id', 'rt.event_id')
        .join('roster_club AS rc', 'rc.roster_club_id', 'rt.roster_club_id')
        .leftJoin('roster_athlete AS ra', (join) => {
            join.on('ra.roster_team_id', 'rt.roster_team_id')
                .andOn('ra.event_id', 'rt.event_id')
                .andOnNull('ra.deleted')
                .andOnNull('ra.deleted_by_user');
        })
        .leftJoin('master_athlete AS ma', (join) => {
            join.on('ma.master_athlete_id', 'ra.master_athlete_id')
        })
        .where('rt.event_id', eventId)
        .where('rt.status_entry', teamsConstants.ENTRY_STATUSES.ACCEPTED)
        .whereNull('rt.deleted')
        .groupBy('rt.roster_team_id', 'rc.roster_club_id')
        .orderBy('rt.team_name');
    
    if (teamClubName) {
        query.whereRaw(`(rt.team_name ILIKE ? OR rc.club_name ILIKE ?)`, [`%${teamClubName}%`, `%${teamClubName}%`]);
    }

    if (teamCode) {
        query.whereRaw(`LOWER(rt.organization_code) = LOWER(?)`, [teamCode]);
    }

    if (state) {
        query.where('rc.state', state);
    }

    if(firstName) {
        query.where(knex.raw(`ma.first ILIKE ?`, [`${firstName}%`]));
    }

    if(lastName) {
        query.where(knex.raw(`ma.last ILIKE ?`, [`${lastName}%`]));
    }

    if(page > 0 && perPage > 0) {
        const limit = perPage;
        const offset = perPage * (page - 1);

        query.limit(limit)
            .offset(offset);
    }

    return Db.query(query).then(result => result?.rows);
}
